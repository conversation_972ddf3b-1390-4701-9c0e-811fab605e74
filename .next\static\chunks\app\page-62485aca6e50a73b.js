(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[931],{7258:function(e,t,s){Promise.resolve().then(s.bind(s,9110))},9110:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return ec}});var a=s(7437),r=s(2265),i=s(1239),l=s(5135),n=s(8997),c=s(7404),o=s(5302),d=s(2252),m=s(7168);let x={"User-Agent":"Mozilla/5.0 BiliDroid/1.12.0 (<EMAIL>)","Content-Type":"application/x-www-form-urlencoded",Accept:"application/json","Accept-Language":"zh-CN,zh;q=0.9,en;q=0.8","x-bili-metadata-legal-region":"CN","x-bili-aurora-eid":"","x-bili-aurora-zone":""},h=null;function u(){let e=Math.floor(Date.now()/1e3),t=Math.random().toString(36).substring(2,15);return"".concat(e,"_").concat(t)}async function p(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};try{let s=new URLSearchParams;Object.entries(t).forEach(e=>{let[t,a]=e;null!=a&&s.append(t,String(a))});let a={...x};h&&(a["x-bili-mid"]=h.mid,a.cookie=h.cookie),a["x-bili-ticket"]=u();let r="".concat(e,"?").concat(s.toString()),i=await fetch(r,{method:"GET",headers:a,mode:"cors"});if(!i.ok)throw Error("HTTP error! status: ".concat(i.status));return await i.json()}catch(e){throw console.error("API GET request failed:",e),e}}async function g(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};try{let s=new URLSearchParams;Object.entries(t).forEach(e=>{let[t,a]=e;null!=a&&s.append(t,String(a))});let a={...x};h&&(a["x-bili-mid"]=h.mid,a.cookie=h.cookie),a["x-bili-ticket"]=u();let r=await fetch(e,{method:"POST",headers:a,body:s,mode:"cors"});if(!r.ok)throw Error("HTTP error! status: ".concat(r.status));return await r.json()}catch(e){throw console.error("API POST request failed:",e),e}}var b=s(1994),j=s(3335);function f(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,j.m6)((0,b.W)(t))}let y={get:e=>{try{let t=localStorage.getItem(e);return t?JSON.parse(t):null}catch(e){return console.error("Error getting item from localStorage:",e),null}},set:(e,t)=>{try{localStorage.setItem(e,JSON.stringify(t))}catch(e){console.error("Error setting item to localStorage:",e)}},remove:e=>{try{localStorage.removeItem(e)}catch(e){console.error("Error removing item from localStorage:",e)}},clear:()=>{try{localStorage.clear()}catch(e){console.error("Error clearing localStorage:",e)}}};function N(e,t){if(!e||"string"!=typeof e)return!1;switch(t){case"deepseek":return e.startsWith("sk-")&&e.length>10;case"gemini":return e.length>20;case"openai":return e.startsWith("sk-")&&e.length>20;default:return!1}}let w={AUTH_DATA:"bili-hardcore-auth",AI_CONFIG:"bili-hardcore-ai-config"};async function v(){try{let e=await fetch("https://passport.bilibili.com/x/passport-login/web/qrcode/generate",{method:"GET",headers:{"User-Agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",Referer:"https://www.bilibili.com/"}});if(!e.ok)throw Error("HTTP error! status: ".concat(e.status));let t=await e.json();if(0!==t.code)throw Error(t.message||"获取二维码失败");return{url:t.data.url,auth_code:t.data.qrcode_key}}catch(e){throw console.error("获取二维码失败:",e),e}}async function k(e){try{let t=await fetch("https://passport.bilibili.com/x/passport-login/web/qrcode/poll?qrcode_key=".concat(e),{method:"GET",headers:{"User-Agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",Referer:"https://www.bilibili.com/"}});if(!t.ok)throw Error("HTTP error! status: ".concat(t.status));return await t.json()}catch(e){throw console.error("轮询二维码状态失败:",e),e}}function _(){try{var e;let t=y.get(w.AUTH_DATA);if(!t)return null;if(e=t.timestamp,Date.now()-e>6048e5)return y.remove(w.AUTH_DATA),null;let s={access_token:t.access_token,csrf:t.csrf,mid:t.mid,cookie:t.cookie};return h=s,s}catch(e){return console.error("加载认证信息失败:",e),null}}async function A(){try{if(!_())return!1;return!0}catch(e){return console.error("验证认证信息失败:",e),!1}}var I=s(5819);async function S(e){try{return await I.toDataURL(e,{errorCorrectionLevel:"M",type:"image/png",quality:.92,margin:1,color:{dark:"#000000",light:"#FFFFFF"},width:256})}catch(e){throw console.error("生成二维码失败:",e),Error("生成二维码失败")}}function E(e){let{onLoginSuccess:t,className:s}=e,[i,l]=(0,r.useState)("idle"),[n,x]=(0,r.useState)(null),[u,p]=(0,r.useState)(""),[g,b]=(0,r.useState)(""),[j,N]=(0,r.useState)(0),_=async()=>{try{l("loading"),b("");let e=await v();x(e);let t=await S(e.url);p(t),l("waiting"),A(e.auth_code)}catch(e){b("获取二维码失败，请重试"),l("error")}},A=e=>{let s=0,a=setInterval(async()=>{try{if(s>=60){clearInterval(a),l("expired");return}let r=await k(e);if(0===r.code&&r.data){clearInterval(a),l("success");let e=r.data.cookie_info.cookies,s="";for(let t of e)if("bili_jct"===t.name){s=t.value;break}let i=e.map(e=>"".concat(e.name,"=").concat(e.value)).join(";"),n={access_token:r.data.access_token,csrf:s,mid:String(r.data.mid),cookie:i};!function(e){try{let t={...e,timestamp:Date.now()};y.set(w.AUTH_DATA,t),h=e}catch(e){throw console.error("保存认证信息失败:",e),e}}(n),t(n)}else 86101===r.code?s++:86090===r.code?(l("scanned"),s++):86038===r.code?(clearInterval(a),l("expired")):(clearInterval(a),b(r.message||"登录失败"),l("error"))}catch(e){++s>=60&&(clearInterval(a),b("网络连接超时，请重试"),l("error"))}},1e3)},I=()=>{N(e=>e+1),_()};return(0,r.useEffect)(()=>{_()},[]),(0,a.jsx)("div",{className:f("card max-w-md mx-auto",s),children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-6",children:"哔哩哔哩登录"}),(0,a.jsx)("div",{className:"mb-6",children:(0,a.jsxs)("div",{className:"relative inline-block",children:[u&&"loading"!==i?(0,a.jsx)("img",{src:u,alt:"登录二维码",className:f("w-64 h-64 border-2 border-gray-200 rounded-lg","expired"===i&&"opacity-50 grayscale")}):(0,a.jsx)("div",{className:"w-64 h-64 border-2 border-gray-200 rounded-lg flex items-center justify-center bg-gray-50",children:(0,a.jsx)("div",{className:"w-8 h-8 loading-spinner"})}),("expired"===i||"error"===i)&&(0,a.jsx)("button",{onClick:I,className:"absolute inset-0 flex items-center justify-center bg-black bg-opacity-50 rounded-lg text-white hover:bg-opacity-60 transition-colors",children:(0,a.jsx)(m.Z,{className:"w-8 h-8"})})]})}),(0,a.jsxs)("div",{className:"flex items-center justify-center gap-2 mb-4",children:[(()=>{switch(i){case"loading":return(0,a.jsx)("div",{className:"w-5 h-5 loading-spinner"});case"waiting":return(0,a.jsx)(c.Z,{className:"w-5 h-5 text-bili-blue"});case"scanned":return(0,a.jsx)(o.Z,{className:"w-5 h-5 text-yellow-500"});case"success":return(0,a.jsx)(o.Z,{className:"w-5 h-5 text-green-500"});case"expired":case"error":return(0,a.jsx)(d.Z,{className:"w-5 h-5 text-red-500"});default:return null}})(),(0,a.jsx)("span",{className:f("text-sm","error"===i||"expired"===i?"text-red-600":"success"===i?"text-green-600":"scanned"===i?"text-yellow-600":"text-gray-600"),children:(()=>{switch(i){case"loading":return"正在生成二维码...";case"waiting":return"请使用哔哩哔哩APP扫描二维码登录";case"scanned":return"扫描成功，请在手机上确认登录";case"success":return"登录成功！";case"expired":return"二维码已过期，请点击刷新";case"error":return g||"发生错误，请重试";default:return""}})()})]}),(0,a.jsxs)("div",{className:"text-xs text-gray-500 space-y-1",children:[(0,a.jsx)("p",{children:"1. 打开哔哩哔哩APP"}),(0,a.jsx)("p",{children:'2. 点击右下角"我的"'}),(0,a.jsx)("p",{children:"3. 点击右上角扫码图标"}),(0,a.jsx)("p",{children:"4. 扫描上方二维码完成登录"})]}),"loading"!==i&&"success"!==i&&(0,a.jsxs)("button",{onClick:I,className:"mt-4 btn-outline px-4 py-2 text-sm",children:[(0,a.jsx)(m.Z,{className:"w-4 h-4 mr-2"}),"刷新二维码"]})]})})}var C=s(2222),P=s(7769),T=s(2208),Z=s(8728);let D="\n当前时间：{timestamp}\n你是一个高效精准的答题专家，面对选择题时，直接根据问题和选项判断正确答案，并返回对应选项的序号（1, 2, 3, 4）。示例：\n问题：大的反义词是什么？\n选项：['长', '宽', '小', '热']\n回答：3\n如果不确定正确答案，选择最接近的选项序号返回，不提供额外解释或超出 1-4 的内容。\n---\n请回答我的问题：{question}\n";class O{async ask(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:3e4,s="".concat(this.baseUrl,"/chat/completions"),a={"Content-Type":"application/json",Authorization:"Bearer ".concat(this.apiKey)},r={model:this.model,messages:[{role:"user",content:D.replace("{timestamp}",Date.now().toString()).replace("{question}",e)}]};try{let e=new AbortController,i=setTimeout(()=>e.abort(),t),l=await fetch(s,{method:"POST",headers:a,body:JSON.stringify(r),signal:e.signal});if(clearTimeout(i),!l.ok)throw Error("DeepSeek API error: ".concat(l.status," ").concat(l.statusText));return(await l.json()).choices[0].message.content.trim()}catch(e){throw console.error("DeepSeek API request failed:",e),Error("DeepSeek API请求失败: ".concat(e instanceof Error?e.message:"未知错误"))}}constructor(e){this.baseUrl="https://api.deepseek.com/v1",this.model="deepseek-chat",this.apiKey=e}}class q{async ask(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:3e4,s="".concat(this.baseUrl,"/models/").concat(this.model,":generateContent?key=").concat(this.apiKey),a={contents:[{parts:[{text:D.replace("{timestamp}",Date.now().toString()).replace("{question}",e)}]}]};try{let e=new AbortController,r=setTimeout(()=>e.abort(),t),i=await fetch(s,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(a),signal:e.signal});if(clearTimeout(r),!i.ok)throw Error("Gemini API error: ".concat(i.status," ").concat(i.statusText));return(await i.json()).candidates[0].content.parts[0].text.trim()}catch(e){throw console.error("Gemini API request failed:",e),Error("Gemini API请求失败: ".concat(e instanceof Error?e.message:"未知错误"))}}constructor(e){this.baseUrl="https://generativelanguage.googleapis.com/v1beta",this.model="gemini-2.0-flash-exp",this.apiKey=e}}class G{async ask(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:3e4,s="".concat(this.baseUrl,"/chat/completions"),a={"Content-Type":"application/json",Authorization:"Bearer ".concat(this.apiKey)},r={model:this.model,messages:[{role:"user",content:D.replace("{timestamp}",Date.now().toString()).replace("{question}",e)}]};try{let e=new AbortController,i=setTimeout(()=>e.abort(),t),l=await fetch(s,{method:"POST",headers:a,body:JSON.stringify(r),signal:e.signal});if(clearTimeout(i),!l.ok)throw Error("OpenAI API error: ".concat(l.status," ").concat(l.statusText));return(await l.json()).choices[0].message.content.trim()}catch(e){throw console.error("OpenAI API request failed:",e),Error("OpenAI API请求失败: ".concat(e instanceof Error?e.message:"未知错误"))}}constructor(e,t,s){this.baseUrl=e.endsWith("/")?e.slice(0,-1):e,this.model=t,this.apiKey=s}}function z(){try{return y.get(w.AI_CONFIG)}catch(e){return console.error("加载AI配置失败:",e),null}}function U(e){var t,s,i;let{onConfigComplete:l,className:n}=e,[c,o]=(0,r.useState)({model_choice:"1"}),[m,x]=(0,r.useState)({deepseek:!1,gemini:!1,openai:!1}),[h,u]=(0,r.useState)({}),[p,g]=(0,r.useState)(!1);(0,r.useEffect)(()=>{let e=z();e&&o(e)},[]);let b=e=>{o(t=>({...t,model_choice:e})),u({})},j=e=>{o(t=>({...t,api_key_deepseek:e})),h.deepseek&&u(e=>({...e,deepseek:""}))},v=e=>{o(t=>({...t,api_key_gemini:e})),h.gemini&&u(e=>({...e,gemini:""}))},k=(e,t)=>{o(s=>({...s,openai_config:{...s.openai_config,[e]:t}})),h.openai&&u(e=>({...e,openai:""}))},_=async()=>{g(!0),u({});try{let e=function(e){switch(e.model_choice){case"1":if(!e.api_key_deepseek)return{isValid:!1,error:"DeepSeek API密钥不能为空"};if(!N(e.api_key_deepseek,"deepseek"))return{isValid:!1,error:"DeepSeek API密钥格式不正确"};break;case"2":if(!e.api_key_gemini)return{isValid:!1,error:"Gemini API密钥不能为空"};if(!N(e.api_key_gemini,"gemini"))return{isValid:!1,error:"Gemini API密钥格式不正确"};break;case"3":if(!e.openai_config)return{isValid:!1,error:"OpenAI配置不能为空"};let{base_url:t,model:s,api_key:a}=e.openai_config;if(!t||!s||!a)return{isValid:!1,error:"OpenAI配置不完整"};if(!N(a,"openai"))return{isValid:!1,error:"OpenAI API密钥格式不正确"};break;default:return{isValid:!1,error:"无效的AI模型选择"}}return{isValid:!0}}(c);if(!e.isValid){u({general:e.error||"配置验证失败"});return}!function(e){try{y.set(w.AI_CONFIG,e)}catch(e){throw console.error("保存AI配置失败:",e),e}}(c),l(c)}catch(e){u({general:"保存配置失败，请重试"})}finally{g(!1)}},A=e=>{x(t=>({...t,[e]:!t[e]}))};return(0,a.jsxs)("div",{className:f("card max-w-2xl mx-auto",n),children:[(0,a.jsxs)("div",{className:"text-center mb-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-center gap-2 mb-2",children:[(0,a.jsx)(C.Z,{className:"w-6 h-6 text-bili-blue"}),(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"AI模型配置"})]}),(0,a.jsx)("p",{className:"text-gray-600",children:"选择并配置用于自动答题的AI模型"})]}),(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:"选择AI模型"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("label",{className:"flex items-center p-3 border rounded-lg cursor-pointer hover:bg-gray-50",children:[(0,a.jsx)("input",{type:"radio",name:"model",value:"1",checked:"1"===c.model_choice,onChange:e=>b(e.target.value),className:"mr-3"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:"DeepSeek (V3)"}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"推荐选择，性价比高"})]})]}),(0,a.jsxs)("label",{className:"flex items-center p-3 border rounded-lg cursor-pointer hover:bg-gray-50",children:[(0,a.jsx)("input",{type:"radio",name:"model",value:"2",checked:"2"===c.model_choice,onChange:e=>b(e.target.value),className:"mr-3"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:"Gemini (2.0-flash)"}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"免费版可能会触发风控429报错"})]})]}),(0,a.jsxs)("label",{className:"flex items-center p-3 border rounded-lg cursor-pointer hover:bg-gray-50",children:[(0,a.jsx)("input",{type:"radio",name:"model",value:"3",checked:"3"===c.model_choice,onChange:e=>b(e.target.value),className:"mr-3"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:"自定义OpenAI格式API"}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"支持OpenAI、火山引擎、硅基流动等"})]})]})]})]}),"1"===c.model_choice&&(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"DeepSeek API密钥"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("input",{type:m.deepseek?"text":"password",value:c.api_key_deepseek||"",onChange:e=>j(e.target.value),placeholder:"请输入DeepSeek API密钥",className:f("input pr-10",h.deepseek&&"border-red-500")}),(0,a.jsx)("button",{type:"button",onClick:()=>A("deepseek"),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",children:m.deepseek?(0,a.jsx)(P.Z,{className:"w-4 h-4"}):(0,a.jsx)(T.Z,{className:"w-4 h-4"})})]}),h.deepseek&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600",children:h.deepseek}),(0,a.jsxs)("p",{className:"mt-1 text-xs text-gray-500",children:["获取地址：",(0,a.jsx)("a",{href:"https://platform.deepseek.com/api_keys",target:"_blank",rel:"noopener noreferrer",className:"text-bili-blue hover:underline",children:"https://platform.deepseek.com/api_keys"})]})]}),"2"===c.model_choice&&(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Gemini API密钥"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("input",{type:m.gemini?"text":"password",value:c.api_key_gemini||"",onChange:e=>v(e.target.value),placeholder:"请输入Gemini API密钥",className:f("input pr-10",h.gemini&&"border-red-500")}),(0,a.jsx)("button",{type:"button",onClick:()=>A("gemini"),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",children:m.gemini?(0,a.jsx)(P.Z,{className:"w-4 h-4"}):(0,a.jsx)(T.Z,{className:"w-4 h-4"})})]}),h.gemini&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600",children:h.gemini}),(0,a.jsxs)("p",{className:"mt-1 text-xs text-gray-500",children:["获取地址：",(0,a.jsx)("a",{href:"https://aistudio.google.com/app/apikey",target:"_blank",rel:"noopener noreferrer",className:"text-bili-blue hover:underline",children:"https://aistudio.google.com/app/apikey"})]})]}),"3"===c.model_choice&&(0,a.jsxs)("div",{className:"mb-6 space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"API基础URL"}),(0,a.jsx)("input",{type:"text",value:(null===(t=c.openai_config)||void 0===t?void 0:t.base_url)||"",onChange:e=>k("base_url",e.target.value),placeholder:"例如：https://ark.cn-beijing.volces.com/api/v3",className:f("input",h.openai&&"border-red-500")})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"模型名称"}),(0,a.jsx)("input",{type:"text",value:(null===(s=c.openai_config)||void 0===s?void 0:s.model)||"",onChange:e=>k("model",e.target.value),placeholder:"例如：deepseek-v3-250324",className:f("input",h.openai&&"border-red-500")}),(0,a.jsx)("p",{className:"mt-1 text-xs text-gray-500",children:"不建议使用思考模型，可能产生意想不到的问题"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"API密钥"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("input",{type:m.openai?"text":"password",value:(null===(i=c.openai_config)||void 0===i?void 0:i.api_key)||"",onChange:e=>k("api_key",e.target.value),placeholder:"请输入API密钥",className:f("input pr-10",h.openai&&"border-red-500")}),(0,a.jsx)("button",{type:"button",onClick:()=>A("openai"),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",children:m.openai?(0,a.jsx)(P.Z,{className:"w-4 h-4"}):(0,a.jsx)(T.Z,{className:"w-4 h-4"})})]})]}),h.openai&&(0,a.jsx)("p",{className:"text-sm text-red-600",children:h.openai})]}),h.general&&(0,a.jsxs)("div",{className:"mb-4 p-3 bg-red-50 border border-red-200 rounded-lg flex items-center gap-2",children:[(0,a.jsx)(d.Z,{className:"w-4 h-4 text-red-500"}),(0,a.jsx)("span",{className:"text-sm text-red-600",children:h.general})]}),(0,a.jsx)("button",{onClick:_,disabled:p,className:"w-full btn-primary py-3 disabled:opacity-50 disabled:cursor-not-allowed",children:p?(0,a.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,a.jsx)("div",{className:"w-4 h-4 loading-spinner border-white"}),(0,a.jsx)("span",{children:"保存中..."})]}):(0,a.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,a.jsx)(Z.Z,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"保存配置"})]})})]})}var F=s(8906),K=s(6362);async function H(){try{let e=await p("https://api.bilibili.com/x/senior/v1/category",{disable_rcmd:0,mobi_app:"android",platform:"android",statistics:'{"appId":1,"platform":3,"version":"8.40.0","abtest":""}',web_location:"333.790"});if(0===e.code&&e.data)return e.data.categories||[];if(41099===e.code)throw Error("获取分类失败，可能是已经达到答题限制(B站每日限制3次)，请前往B站APP确认是否可以正常答题");throw Error(e.message||"获取分类失败，请前往B站APP确认是否可以正常答题")}catch(e){throw console.error("获取分类失败:",e),e}}async function V(){try{let e=await p("https://api.bilibili.com/x/senior/v1/captcha",{disable_rcmd:0,mobi_app:"android",platform:"android",statistics:'{"appId":1,"platform":3,"version":"8.40.0","abtest":""}',web_location:"333.790"});if(0===e.code&&e.data)return e.data;throw Error(e.message||"获取验证码失败，请前往B站APP确认是否可以正常答题")}catch(e){throw console.error("获取验证码失败:",e),e}}async function W(e,t,s){try{let a=await g("https://api.bilibili.com/x/senior/v1/captcha/submit",{bili_code:e,bili_token:t,disable_rcmd:"0",gt_challenge:"",gt_seccode:"",gt_validate:"",ids:s,mobi_app:"android",platform:"android",statistics:'{"appId":1,"platform":3,"version":"8.40.0","abtest":""}',type:"bilibili"});return 0===a.code}catch(e){throw console.error("提交验证码失败:",e),e}}async function L(){try{return await p("https://api.bilibili.com/x/senior/v1/question",{disable_rcmd:"0",mobi_app:"android",platform:"android",statistics:'{"appId":1,"platform":3,"version":"8.40.0","abtest":""}',web_location:"333.790"})}catch(e){throw console.error("获取题目失败:",e),e}}async function M(e,t,s){try{return await g("https://api.bilibili.com/x/senior/v1/answer/submit",{id:e,ans_hash:t,ans_text:s,disable_rcmd:"0",mobi_app:"android",platform:"android",statistics:'{"appId":1,"platform":3,"version":"8.40.0","abtest":""}',web_location:"333.790"})}catch(e){throw console.error("提交答案失败:",e),e}}async function B(){try{let e=await p("https://api.bilibili.com/x/senior/v1/answer/result",{disable_rcmd:"0",mobi_app:"android",platform:"android",statistics:'{"appId":1,"platform":3,"version":"8.40.0","abtest":""}',web_location:"333.790"});if(0===e.code&&e.data)return e.data;throw Error(e.message||"获取答题结果失败")}catch(e){throw console.error("获取答题结果失败:",e),e}}function R(e){let{onCaptchaComplete:t,className:s}=e,[i,l]=(0,r.useState)("loading"),[n,c]=(0,r.useState)([]),[x,h]=(0,r.useState)([]),[u,p]=(0,r.useState)(null),[g,b]=(0,r.useState)(""),[j,y]=(0,r.useState)(""),N=async()=>{try{l("loading"),y("");let e=await H();c(e),l("selecting")}catch(e){y(e instanceof Error?e.message:"获取分类失败"),l("error")}},w=async()=>{try{l("loading"),y("");let e=await V();p(e),l("captcha")}catch(e){y(e instanceof Error?e.message:"获取验证码失败"),l("error")}},v=e=>{h(t=>t.includes(e)?t.filter(t=>t!==e):t.length>=3?t:[...t,e])},k=async()=>{if(!g.trim()){y("请输入验证码");return}if(!u){y("验证码数据丢失，请重新获取");return}try{l("submitting"),y("");let e=x.join(",");if(await W(g,u.token,e))t(e);else throw Error("验证码验证失败")}catch(e){y(e instanceof Error?e.message:"验证码提交失败"),l("captcha")}};return(0,r.useEffect)(()=>{N()},[]),(0,a.jsxs)("div",{className:f("card max-w-2xl mx-auto",s),children:[(0,a.jsxs)("div",{className:"text-center mb-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-center gap-2 mb-2",children:[(()=>{switch(i){case"loading":case"submitting":return(0,a.jsx)("div",{className:"w-5 h-5 loading-spinner"});case"selecting":case"captcha":return(0,a.jsx)(F.Z,{className:"w-5 h-5 text-bili-blue"});case"error":return(0,a.jsx)(d.Z,{className:"w-5 h-5 text-red-500"});default:return null}})(),(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"验证码验证"})]}),(0,a.jsx)("p",{className:"text-gray-600",children:(()=>{switch(i){case"loading":return"正在加载...";case"selecting":return"请选择答题分类";case"captcha":return"请输入验证码";case"submitting":return"正在验证...";case"error":return"发生错误";default:return""}})()})]}),"selecting"===i&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-3",children:"选择答题分类"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:"请选择1-3个分类（最多3个），建议选择知识区和历史区以获得更高正确率"}),(0,a.jsx)("div",{className:"grid grid-cols-2 gap-3",children:n.map(e=>(0,a.jsx)("button",{onClick:()=>v(e.id),disabled:!x.includes(e.id)&&x.length>=3,className:f("p-3 text-left border rounded-lg transition-colors","hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",x.includes(e.id)?"border-bili-pink bg-bili-pink/5":"border-gray-200"),children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[x.includes(e.id)&&(0,a.jsx)(o.Z,{className:"w-4 h-4 text-bili-pink"}),(0,a.jsx)("span",{className:"text-sm font-medium",children:e.name})]})},e.id))})]}),(0,a.jsx)("div",{className:"text-center",children:(0,a.jsxs)("button",{onClick:()=>{if(0===x.length){y("请至少选择一个分类");return}w()},disabled:0===x.length,className:"btn-primary px-6 py-2 disabled:opacity-50 disabled:cursor-not-allowed",children:["确认选择 (",x.length,"/3)"]})})]}),"captcha"===i&&u&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-3",children:"输入验证码"}),(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-2",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"验证码图片："}),(0,a.jsxs)("button",{onClick:()=>{b(""),w()},className:"btn-outline px-2 py-1 text-xs",children:[(0,a.jsx)(m.Z,{className:"w-3 h-3 mr-1"}),"刷新"]})]}),(0,a.jsxs)("a",{href:u.url,target:"_blank",rel:"noopener noreferrer",className:"inline-flex items-center gap-2 p-3 bg-gray-50 border border-gray-200 rounded-lg hover:bg-gray-100 transition-colors",children:[(0,a.jsx)(K.Z,{className:"w-4 h-4 text-bili-blue"}),(0,a.jsx)("span",{className:"text-sm text-bili-blue",children:"点击查看验证码"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"验证码"}),(0,a.jsx)("input",{type:"text",value:g,onChange:e=>b(e.target.value),placeholder:"请输入验证码",className:"input",disabled:!1,onKeyPress:e=>{"Enter"===e.key&&k()}})]})]}),(0,a.jsx)("div",{className:"text-center",children:(0,a.jsx)("button",{onClick:k,disabled:!g.trim(),className:"btn-primary px-6 py-2 disabled:opacity-50 disabled:cursor-not-allowed",children:"提交验证码"})})]}),j&&(0,a.jsxs)("div",{className:"mt-4 p-4 bg-red-50 border border-red-200 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,a.jsx)(d.Z,{className:"w-4 h-4 text-red-500"}),(0,a.jsx)("span",{className:"text-sm font-medium text-red-800",children:"错误"})]}),(0,a.jsx)("p",{className:"text-sm text-red-700 mb-2",children:j}),(0,a.jsx)("button",{onClick:()=>{y(""),"error"===i&&0===n.length?N():"error"!==i||u?l("captcha"):w()},className:"btn-outline px-3 py-1 text-sm",children:"重试"})]}),(0,a.jsx)("div",{className:"mt-6 p-3 bg-blue-50 border border-blue-200 rounded-lg",children:(0,a.jsxs)("div",{className:"text-sm text-blue-800",children:[(0,a.jsx)("p",{className:"font-medium mb-1",children:"提示："}),(0,a.jsxs)("ul",{className:"space-y-1 text-xs",children:[(0,a.jsx)("li",{children:"• 验证码用于确认您的答题分类选择"}),(0,a.jsx)("li",{children:"• 建议选择知识区和历史区，正确率更高"}),(0,a.jsx)("li",{children:"• 每日最多可以答题3次"})]})]})})]})}var J=s(4972),Q=s(9474),X=s(3639),Y=s(1723),$=s(5131);function ee(e){let{aiConfig:t,onQuizComplete:s,className:i}=e,[l,n]=(0,r.useState)("idle"),[c,d]=(0,r.useState)(null),[m,x]=(0,r.useState)(0),[h,u]=(0,r.useState)(0),[p,g]=(0,r.useState)(""),[b,j]=(0,r.useState)(null),[y,N]=(0,r.useState)(""),[w,v]=(0,r.useState)(!0),k=async()=>{try{n("loading"),N(""),g(""),j(null);let e=await L();if(0!==e.code){if(41103===e.code){N("答题已结束或您已经是硬核会员"),n("completed");return}throw Error(e.message||"获取题目失败")}if(!e.data)throw Error("题目数据为空");d(e.data),x(e.data.question_num),n("answering"),w&&await _(e.data)}catch(e){N(e instanceof Error?e.message:"获取题目失败"),n("error")}},_=async e=>{try{let s=function(e){switch(e.model_choice){case"1":if(!e.api_key_deepseek)throw Error("DeepSeek API密钥未配置");return new O(e.api_key_deepseek);case"2":if(!e.api_key_gemini)throw Error("Gemini API密钥未配置");return new q(e.api_key_gemini);case"3":if(!e.openai_config)throw Error("OpenAI配置未完整");return new G(e.openai_config.base_url,e.openai_config.model,e.openai_config.api_key);default:throw Error("无效的AI模型选择")}}(t),a=function(e){let t=e.answers.map((e,t)=>"".concat(t+1,". ").concat(e.ans_text)).join("\n");return"题目: ".concat(e.question,"\n选项:\n").concat(t)}(e),r=await s.ask(a);g(r);let i=function(e,t){let s=e.trim(),a=s.match(/\b([1-4])\b/);if(a){let e=parseInt(a[1]);if(e>=1&&e<=t)return e}let r=s.charAt(0);if(/[1-4]/.test(r)){let e=parseInt(r);if(e>=1&&e<=t)return e}return null}(r,e.answers.length);if(i&&i>=1&&i<=e.answers.length){let t=e.answers[i-1];j(t),w&&setTimeout(()=>{A(e.id,t)},1e3)}else N("AI回答无效: ".concat(r))}catch(e){N("AI回答失败: ".concat(e instanceof Error?e.message:"未知错误"))}},A=async(e,t)=>{try{n("submitting");let a=await M(e,t.ans_hash,t.ans_text);if(0===a.code){let e=(await B()).score;e>h&&u(e),setTimeout(k,2e3)}else if(41103===a.code){n("completed");let e=await B();s(e)}else throw Error(a.message||"提交答案失败")}catch(e){N(e instanceof Error?e.message:"提交答案失败"),n("error")}},I=e=>{c&&"submitting"!==l&&(j(e),w||A(c.id,e))},S=()=>{x(0),u(0),k()};return(0,r.useEffect)(()=>{S()},[]),(0,a.jsxs)("div",{className:f("card max-w-4xl mx-auto",i),children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(()=>{switch(l){case"loading":case"submitting":return(0,a.jsx)("div",{className:"w-5 h-5 loading-spinner"});case"answering":return(0,a.jsx)(J.Z,{className:"w-5 h-5 text-bili-blue"});case"completed":return(0,a.jsx)(Q.Z,{className:"w-5 h-5 text-yellow-500"});case"error":return(0,a.jsx)(X.Z,{className:"w-5 h-5 text-red-500"});default:return null}})(),(0,a.jsx)("h2",{className:"text-xl font-bold text-gray-900",children:m>0?"第".concat(m,"题"):"答题系统"})]}),(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:["当前得分: ",(0,a.jsx)("span",{className:"font-medium text-bili-pink",children:h})]})]}),(0,a.jsx)("div",{className:"flex items-center gap-2",children:(0,a.jsxs)("label",{className:"flex items-center gap-2 text-sm",children:[(0,a.jsx)("input",{type:"checkbox",checked:w,onChange:e=>v(e.target.checked),disabled:"submitting"===l,className:"rounded"}),(0,a.jsx)("span",{children:"AI自动答题"})]})})]}),(0,a.jsx)("div",{className:"mb-4 p-3 bg-gray-50 rounded-lg",children:(0,a.jsxs)("div",{className:"flex items-center gap-2 text-sm text-gray-600",children:[(0,a.jsx)(Y.Z,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:(()=>{switch(l){case"loading":return"正在获取题目...";case"answering":return w?"AI正在思考...":"请选择答案";case"submitting":return"正在提交答案...";case"completed":return"答题已完成";case"error":return"发生错误";default:return""}})()})]})}),c&&(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"题目"}),(0,a.jsx)("p",{className:"text-gray-700 leading-relaxed",children:c.question})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("h4",{className:"text-md font-medium text-gray-900",children:"选项"}),c.answers.map((e,t)=>(0,a.jsx)("button",{onClick:()=>I(e),disabled:"submitting"===l||w&&"answering"===l,className:f("w-full p-4 text-left border rounded-lg transition-colors","hover:bg-gray-50 disabled:cursor-not-allowed",(null==b?void 0:b.ans_hash)===e.ans_hash?"border-bili-pink bg-bili-pink/5":"border-gray-200"),children:(0,a.jsxs)("div",{className:"flex items-start gap-3",children:[(0,a.jsx)("span",{className:"flex-shrink-0 w-6 h-6 bg-gray-100 rounded-full flex items-center justify-center text-sm font-medium",children:t+1}),(0,a.jsx)("span",{className:"text-gray-700",children:e.ans_text}),(null==b?void 0:b.ans_hash)===e.ans_hash&&(0,a.jsx)(o.Z,{className:"flex-shrink-0 w-5 h-5 text-bili-pink ml-auto"})]})},t))]}),p&&(0,a.jsxs)("div",{className:"mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,a.jsx)(J.Z,{className:"w-4 h-4 text-blue-600"}),(0,a.jsx)("span",{className:"text-sm font-medium text-blue-800",children:"AI回答"})]}),(0,a.jsx)("p",{className:"text-sm text-blue-700",children:p})]})]}),y&&(0,a.jsxs)("div",{className:"mb-4 p-4 bg-red-50 border border-red-200 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,a.jsx)($.Z,{className:"w-4 h-4 text-red-500"}),(0,a.jsx)("span",{className:"text-sm font-medium text-red-800",children:"错误"})]}),(0,a.jsx)("p",{className:"text-sm text-red-700",children:y}),(0,a.jsx)("button",{onClick:()=>{N(""),c?w?_(c):n("answering"):k()},className:"mt-2 btn-outline px-3 py-1 text-sm",children:"重试"})]}),!w&&b&&"answering"===l&&(0,a.jsx)("div",{className:"text-center",children:(0,a.jsx)("button",{onClick:()=>c&&A(c.id,b),className:"btn-primary px-6 py-2",children:"提交答案"})})]})}var et=s(6595),es=s(9076),ea=s(8930);function er(e){let{result:t,onRestart:s,className:i}=e,[l,n]=(0,r.useState)(!1),[c,o]=(0,r.useState)(!1),d=t.score>=60,m=t.scores.reduce((e,t)=>e+t.total,0),x=async()=>{try{o(!0),function(){try{y.remove(w.AUTH_DATA),h=null}catch(e){console.error("清除认证信息失败:",e)}}(),function(){try{y.remove(w.AI_CONFIG)}catch(e){console.error("清除AI配置失败:",e)}}(),y.clear(),setTimeout(()=>{n(!1),o(!1),window.location.reload()},1e3)}catch(e){console.error("清除数据失败:",e),o(!1)}};return(0,a.jsxs)("div",{className:f("card max-w-2xl mx-auto",i),children:[(0,a.jsxs)("div",{className:"text-center mb-6",children:[(0,a.jsx)("div",{className:"flex items-center justify-center gap-2 mb-4",children:d?(0,a.jsx)("div",{className:"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center",children:(0,a.jsx)(Q.Z,{className:"w-8 h-8 text-green-600"})}):(0,a.jsx)("div",{className:"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center",children:(0,a.jsx)($.Z,{className:"w-8 h-8 text-red-600"})})}),(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-2",children:d?"\uD83C\uDF89 恭喜通过！":"\uD83D\uDE14 未能通过"}),(0,a.jsx)("p",{className:"text-gray-600",children:d?"您已成功通过哔哩哔哩硬核会员答题！":"运气稍微有点差，建议重新答题，知识区和历史区的正确率会更高"})]}),(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsxs)("div",{className:"text-center mb-4",children:[(0,a.jsx)("div",{className:"text-4xl font-bold text-bili-pink mb-1",children:t.score}),(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:["总分 / ",m]})]}),(0,a.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-3 mb-4",children:(0,a.jsx)("div",{className:f("h-3 rounded-full transition-all duration-1000",d?"bg-green-500":"bg-red-500"),style:{width:"".concat(Math.min(t.score/m*100,100),"%")}})}),(0,a.jsxs)("div",{className:"flex justify-between text-xs text-gray-500 mb-4",children:[(0,a.jsx)("span",{children:"0"}),(0,a.jsxs)("span",{className:"relative",children:["60 (及格线)",(0,a.jsx)("div",{className:"absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-6",children:(0,a.jsx)("div",{className:"w-px h-4 bg-yellow-400"})})]}),(0,a.jsx)("span",{children:m})]})]}),(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-3",children:"分类得分详情"}),(0,a.jsx)("div",{className:"space-y-3",children:t.scores.map((e,t)=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(et.Z,{className:"w-4 h-4 text-yellow-500"}),(0,a.jsx)("span",{className:"font-medium text-gray-900",children:e.category})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsxs)("div",{className:"font-medium text-gray-900",children:[e.score," / ",e.total]}),(0,a.jsxs)("div",{className:"text-xs text-gray-500",children:[(e.score/e.total*100).toFixed(1),"%"]})]})]},t))})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("button",{onClick:s,className:"w-full btn-primary py-3",children:[(0,a.jsx)(es.Z,{className:"w-4 h-4 mr-2"}),"重新答题"]}),d&&(0,a.jsxs)("button",{onClick:()=>n(!0),className:"w-full btn-outline py-3",children:[(0,a.jsx)(ea.Z,{className:"w-4 h-4 mr-2"}),"清除登录信息和API密钥"]})]}),(0,a.jsx)("div",{className:"mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg",children:(0,a.jsxs)("div",{className:"text-sm text-blue-800",children:[(0,a.jsx)("p",{className:"font-medium mb-2",children:d?"\uD83C\uDF8A 成功提示：":"\uD83D\uDCA1 重试建议："}),d?(0,a.jsxs)("ul",{className:"space-y-1 text-xs",children:[(0,a.jsx)("li",{children:"• 您已成功获得哔哩哔哩硬核会员资格"}),(0,a.jsx)("li",{children:"• 为了保护您的隐私，建议清除本地保存的登录信息"}),(0,a.jsx)("li",{children:"• 感谢使用本工具，祝您使用愉快！"})]}):(0,a.jsxs)("ul",{className:"space-y-1 text-xs",children:[(0,a.jsx)("li",{children:"• 建议选择知识区和历史区，这些分类的正确率更高"}),(0,a.jsx)("li",{children:"• 每日最多可以答题3次，请合理安排"}),(0,a.jsx)("li",{children:"• 可以尝试更换不同的AI模型来提高准确率"})]})]})}),l&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg p-6 max-w-md mx-4",children:[(0,a.jsxs)("div",{className:"text-center mb-4",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-3",children:(0,a.jsx)(ea.Z,{className:"w-6 h-6 text-yellow-600"})}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"确认清除数据"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"此操作将清除所有本地保存的登录信息和API密钥，确保您的隐私安全。此操作不可撤销。"})]}),(0,a.jsxs)("div",{className:"flex gap-3",children:[(0,a.jsx)("button",{onClick:()=>n(!1),disabled:c,className:"flex-1 btn-outline py-2 disabled:opacity-50",children:"取消"}),(0,a.jsx)("button",{onClick:x,disabled:c,className:"flex-1 btn-primary py-2 disabled:opacity-50",children:c?(0,a.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,a.jsx)("div",{className:"w-4 h-4 loading-spinner border-white"}),(0,a.jsx)("span",{children:"清除中..."})]}):"确认清除"})]})]})})]})}class ei extends r.Component{static getDerivedStateFromError(e){return{hasError:!0,error:e}}componentDidCatch(e,t){console.error("ErrorBoundary caught an error:",e,t)}render(){if(this.state.hasError){if(this.props.fallback){let e=this.props.fallback;return(0,a.jsx)(e,{error:this.state.error,resetError:this.resetError})}return(0,a.jsx)(el,{error:this.state.error,resetError:this.resetError})}return this.props.children}constructor(e){super(e),this.resetError=()=>{this.setState({hasError:!1,error:void 0})},this.state={hasError:!1}}}function el(e){let{error:t,resetError:s}=e;return(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,a.jsxs)("div",{className:"max-w-md mx-auto text-center p-6",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,a.jsx)(X.Z,{className:"w-8 h-8 text-red-600"})}),(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:"出现了一些问题"}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:"应用遇到了意外错误，请尝试刷新页面或重新开始。"}),t&&(0,a.jsxs)("details",{className:"mb-6 text-left",children:[(0,a.jsx)("summary",{className:"cursor-pointer text-sm text-gray-500 hover:text-gray-700",children:"查看错误详情"}),(0,a.jsxs)("pre",{className:"mt-2 p-3 bg-gray-100 rounded text-xs text-gray-700 overflow-auto",children:[t.message,t.stack&&"\n\n"+t.stack]})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("button",{onClick:s,className:"w-full btn-primary py-2",children:[(0,a.jsx)(m.Z,{className:"w-4 h-4 mr-2"}),"重试"]}),(0,a.jsx)("button",{onClick:()=>window.location.reload(),className:"w-full btn-outline py-2",children:"刷新页面"})]}),(0,a.jsx)("p",{className:"mt-6 text-xs text-gray-500",children:"如果问题持续存在，请尝试清除浏览器缓存或联系技术支持。"})]})})}function en(e){let{size:t="md",className:s,text:r}=e;return(0,a.jsx)("div",{className:f("flex items-center justify-center",s),children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:f("loading-spinner mx-auto",{sm:"w-4 h-4",md:"w-6 h-6",lg:"w-8 h-8"}[t])}),r&&(0,a.jsx)("p",{className:"mt-2 text-sm text-gray-600",children:r})]})})}function ec(){let[e,t]=(0,r.useState)("login"),[s,c]=(0,r.useState)(null),[o,d]=(0,r.useState)(null),[m,x]=(0,r.useState)(null),[u,p]=(0,r.useState)(!0);return((0,r.useEffect)(()=>{(async()=>{try{let e=_();if(e){if(await A()){c(e),h=e;let s=z();s?(d(s),t("quiz")):t("ai-config")}else t("login")}else t("login")}catch(e){console.error("初始化应用失败:",e),t("login")}finally{p(!1)}})()},[]),u)?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsx)(en,{size:"lg",text:"正在初始化..."})}):(0,a.jsx)(ei,{children:(0,a.jsxs)("div",{className:"min-h-screen",children:[(0,a.jsx)("header",{className:"bg-white/80 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-40",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"flex items-center justify-between h-16",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-bili-pink rounded-lg flex items-center justify-center",children:(0,a.jsx)(i.Z,{className:"w-5 h-5 text-white"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-lg font-bold text-gray-900",children:"哔哩哔哩硬核会员答题助手"}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:"基于AI的自动答题工具"})]})]}),(0,a.jsx)("div",{className:"flex items-center gap-4",children:(0,a.jsxs)("a",{href:"https://github.com/Karben233/bili-hardcore",target:"_blank",rel:"noopener noreferrer",className:"flex items-center gap-2 text-sm text-gray-600 hover:text-gray-900 transition-colors",children:[(0,a.jsx)(l.Z,{className:"w-4 h-4"}),(0,a.jsx)("span",{className:"hidden sm:inline",children:"GitHub"})]})})]})})}),(0,a.jsxs)("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,a.jsx)("div",{className:"mb-8",children:(0,a.jsx)("div",{className:"flex items-center justify-center",children:(0,a.jsx)("div",{className:"flex items-center space-x-4",children:[{key:"login",label:"登录",step:1},{key:"ai-config",label:"AI配置",step:2},{key:"quiz",label:"答题",step:3},{key:"result",label:"结果",step:4}].map((t,s)=>(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ".concat(e===t.key?"bg-bili-pink text-white":["login","ai-config","quiz"].indexOf(e)>["login","ai-config","quiz"].indexOf(t.key)?"bg-green-500 text-white":"bg-gray-200 text-gray-600"),children:t.step}),(0,a.jsx)("span",{className:"ml-2 text-sm ".concat(e===t.key?"text-bili-pink font-medium":"text-gray-500"),children:t.label}),s<3&&(0,a.jsx)("div",{className:"w-8 h-px bg-gray-300 mx-4"})]},t.key))})})}),(0,a.jsxs)("div",{className:"animate-fade-in",children:["login"===e&&(0,a.jsx)(E,{onLoginSuccess:e=>{c(e),h=e,t("ai-config")}}),"ai-config"===e&&(0,a.jsx)(U,{onConfigComplete:e=>{d(e),t("quiz")}}),"captcha"===e&&(0,a.jsx)(R,{onCaptchaComplete:e=>{t("quiz")}}),"quiz"===e&&o&&(0,a.jsx)(ee,{aiConfig:o,onQuizComplete:e=>{x(e),t("result")}}),"result"===e&&m&&(0,a.jsx)(er,{result:m,onRestart:()=>{x(null),t("quiz")}})]})]}),(0,a.jsx)("footer",{className:"bg-white/80 backdrop-blur-sm border-t border-gray-200 mt-16",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("div",{className:"flex items-center justify-center gap-2 mb-4",children:[(0,a.jsx)(n.Z,{className:"w-4 h-4 text-red-500"}),(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"本软件免费且代码开源"})]}),(0,a.jsxs)("div",{className:"space-y-2 text-xs text-gray-500",children:[(0,a.jsxs)("p",{children:["源码地址：",(0,a.jsx)("a",{href:"https://github.com/Karben233/bili-hardcore",target:"_blank",rel:"noopener noreferrer",className:"text-bili-blue hover:underline ml-1",children:"https://github.com/Karben233/bili-hardcore"})]}),(0,a.jsx)("p",{children:"问题反馈：请在GitHub仓库中提交Issue"}),(0,a.jsx)("p",{className:"text-gray-400",children:"免责声明：本工具仅供学习交流使用，请遵守相关平台的使用条款"})]})]})})})]})})}}},function(e){e.O(0,[997,971,117,744],function(){return e(e.s=7258)}),_N_E=e.O()}]);