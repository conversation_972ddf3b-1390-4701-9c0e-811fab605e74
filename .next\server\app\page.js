(()=>{var e={};e.id=931,e.ids=[931],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},7790:e=>{"use strict";e.exports=require("assert")},8893:e=>{"use strict";e.exports=require("buffer")},2048:e=>{"use strict";e.exports=require("fs")},6162:e=>{"use strict";e.exports=require("stream")},1764:e=>{"use strict";e.exports=require("util")},1568:e=>{"use strict";e.exports=require("zlib")},6739:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>u,originalPathname:()=>h,pages:()=>d,routeModule:()=>p,tree:()=>c}),r(5480),r(2029),r(5866);var i=r(3191),n=r(8716),s=r(7922),a=r.n(s),o=r(5231),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let c=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,5480)),"E:\\Temp\\bili-hardcore\\src\\app\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,2029)),"E:\\Temp\\bili-hardcore\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,5866,23)),"next/dist/client/components/not-found-error"]}],d=["E:\\Temp\\bili-hardcore\\src\\app\\page.tsx"],h="/page",u={require:r,loadChunk:()=>Promise.resolve()},p=new i.AppPageRouteModule({definition:{kind:n.x.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},2051:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,2994,23)),Promise.resolve().then(r.t.bind(r,6114,23)),Promise.resolve().then(r.t.bind(r,9727,23)),Promise.resolve().then(r.t.bind(r,9671,23)),Promise.resolve().then(r.t.bind(r,1868,23)),Promise.resolve().then(r.t.bind(r,4759,23))},6748:()=>{},1793:(e,t,r)=>{Promise.resolve().then(r.bind(r,2091))},4414:e=>{"use strict";var t={single_source_shortest_paths:function(e,r,i){var n,s,a,o,l,c,d,h={},u={};u[r]=0;var p=t.PriorityQueue.make();for(p.push(r,0);!p.empty();)for(a in s=(n=p.pop()).value,o=n.cost,l=e[s]||{})l.hasOwnProperty(a)&&(c=o+l[a],d=u[a],(void 0===u[a]||d>c)&&(u[a]=c,p.push(a,c),h[a]=s));if(void 0!==i&&void 0===u[i])throw Error(["Could not find a path from ",r," to ",i,"."].join(""));return h},extract_shortest_path_from_predecessor_list:function(e,t){for(var r=[],i=t;i;)r.push(i),e[i],i=e[i];return r.reverse(),r},find_path:function(e,r,i){var n=t.single_source_shortest_paths(e,r,i);return t.extract_shortest_path_from_predecessor_list(n,i)},PriorityQueue:{make:function(e){var r,i=t.PriorityQueue,n={};for(r in e=e||{},i)i.hasOwnProperty(r)&&(n[r]=i[r]);return n.queue=[],n.sorter=e.sorter||i.default_sorter,n},default_sorter:function(e,t){return e.cost-t.cost},push:function(e,t){this.queue.push({value:e,cost:t}),this.queue.sort(this.sorter)},pop:function(){return this.queue.shift()},empty:function(){return 0===this.queue.length}}};e.exports=t},1349:(e,t,r)=>{"use strict";let i=r(1289),n=[function(){},function(e,t,r,i){if(i===t.length)throw Error("Ran out of data");let n=t[i];e[r]=n,e[r+1]=n,e[r+2]=n,e[r+3]=255},function(e,t,r,i){if(i+1>=t.length)throw Error("Ran out of data");let n=t[i];e[r]=n,e[r+1]=n,e[r+2]=n,e[r+3]=t[i+1]},function(e,t,r,i){if(i+2>=t.length)throw Error("Ran out of data");e[r]=t[i],e[r+1]=t[i+1],e[r+2]=t[i+2],e[r+3]=255},function(e,t,r,i){if(i+3>=t.length)throw Error("Ran out of data");e[r]=t[i],e[r+1]=t[i+1],e[r+2]=t[i+2],e[r+3]=t[i+3]}],s=[function(){},function(e,t,r,i){let n=t[0];e[r]=n,e[r+1]=n,e[r+2]=n,e[r+3]=i},function(e,t,r){let i=t[0];e[r]=i,e[r+1]=i,e[r+2]=i,e[r+3]=t[1]},function(e,t,r,i){e[r]=t[0],e[r+1]=t[1],e[r+2]=t[2],e[r+3]=i},function(e,t,r){e[r]=t[0],e[r+1]=t[1],e[r+2]=t[2],e[r+3]=t[3]}];t.dataToBitMap=function(e,t){let r,a,o,l,c=t.width,d=t.height,h=t.depth,u=t.bpp,p=t.interlace;if(8!==h){let t,i;t=[],i=0,r={get:function(r){for(;t.length<r;)!function(){let r,n,s,a,o,l,c,d;if(i===e.length)throw Error("Ran out of data");let u=e[i];switch(i++,h){default:throw Error("unrecognised depth");case 16:c=e[i],i++,t.push((u<<8)+c);break;case 4:c=15&u,d=u>>4,t.push(d,c);break;case 2:o=3&u,l=u>>2&3,c=u>>4&3,d=u>>6&3,t.push(d,c,l,o);break;case 1:r=1&u,n=u>>1&1,s=u>>2&1,a=u>>3&1,o=u>>4&1,l=u>>5&1,c=u>>6&1,d=u>>7&1,t.push(d,c,l,o,a,s,n,r)}}();let n=t.slice(0,r);return t=t.slice(r),n},resetAfterLine:function(){t.length=0},end:function(){if(i!==e.length)throw Error("extra data found")}}}a=h<=8?Buffer.alloc(c*d*4):new Uint16Array(c*d*4);let f=Math.pow(2,h)-1,m=0;if(p)o=i.getImagePasses(c,d),l=i.getInterlaceIterator(c,d);else{let e=0;l=function(){let t=e;return e+=4,t},o=[{width:c,height:d}]}for(let t=0;t<o.length;t++)8===h?m=function(e,t,r,i,s,a){let o=e.width,l=e.height,c=e.index;for(let e=0;e<l;e++)for(let l=0;l<o;l++){let o=r(l,e,c);n[i](t,s,o,a),a+=i}return a}(o[t],a,l,u,e,m):function(e,t,r,i,n,a){let o=e.width,l=e.height,c=e.index;for(let e=0;e<l;e++){for(let l=0;l<o;l++){let o=n.get(i),d=r(l,e,c);s[i](t,o,d,a)}n.resetAfterLine()}}(o[t],a,l,u,r,f);if(8===h){if(m!==e.length)throw Error("extra data found")}else r.end();return a}},3333:(e,t,r)=>{"use strict";let i=r(2949);e.exports=function(e,t,r,n){let s=-1!==[i.COLORTYPE_COLOR_ALPHA,i.COLORTYPE_ALPHA].indexOf(n.colorType);if(n.colorType===n.inputColorType){let t;let r=(new DataView(t=new ArrayBuffer(2)).setInt16(0,256,!0),256!==new Int16Array(t)[0]);if(8===n.bitDepth||16===n.bitDepth&&r)return e}let a=16!==n.bitDepth?e:new Uint16Array(e.buffer),o=255,l=i.COLORTYPE_TO_BPP_MAP[n.inputColorType];4!==l||n.inputHasAlpha||(l=3);let c=i.COLORTYPE_TO_BPP_MAP[n.colorType];16===n.bitDepth&&(o=65535,c*=2);let d=Buffer.alloc(t*r*c),h=0,u=0,p=n.bgColor||{};void 0===p.red&&(p.red=o),void 0===p.green&&(p.green=o),void 0===p.blue&&(p.blue=o);for(let e=0;e<r;e++)for(let e=0;e<t;e++){let e=function(){let e,t,r;let l=o;switch(n.inputColorType){case i.COLORTYPE_COLOR_ALPHA:l=a[h+3],e=a[h],t=a[h+1],r=a[h+2];break;case i.COLORTYPE_COLOR:e=a[h],t=a[h+1],r=a[h+2];break;case i.COLORTYPE_ALPHA:l=a[h+1],t=e=a[h],r=e;break;case i.COLORTYPE_GRAYSCALE:t=e=a[h],r=e;break;default:throw Error("input color type:"+n.inputColorType+" is not supported at present")}return n.inputHasAlpha&&!s&&(l/=o,e=Math.min(Math.max(Math.round((1-l)*p.red+l*e),0),o),t=Math.min(Math.max(Math.round((1-l)*p.green+l*t),0),o),r=Math.min(Math.max(Math.round((1-l)*p.blue+l*r),0),o)),{red:e,green:t,blue:r,alpha:l}}(a,h);switch(n.colorType){case i.COLORTYPE_COLOR_ALPHA:case i.COLORTYPE_COLOR:8===n.bitDepth?(d[u]=e.red,d[u+1]=e.green,d[u+2]=e.blue,s&&(d[u+3]=e.alpha)):(d.writeUInt16BE(e.red,u),d.writeUInt16BE(e.green,u+2),d.writeUInt16BE(e.blue,u+4),s&&d.writeUInt16BE(e.alpha,u+6));break;case i.COLORTYPE_ALPHA:case i.COLORTYPE_GRAYSCALE:{let t=(e.red+e.green+e.blue)/3;8===n.bitDepth?(d[u]=t,s&&(d[u+1]=e.alpha)):(d.writeUInt16BE(t,u),s&&d.writeUInt16BE(e.alpha,u+2));break}default:throw Error("unrecognised color Type "+n.colorType)}h+=l,u+=c}return d}},2506:(e,t,r)=>{"use strict";let i=r(1764),n=r(6162),s=e.exports=function(){n.call(this),this._buffers=[],this._buffered=0,this._reads=[],this._paused=!1,this._encoding="utf8",this.writable=!0};i.inherits(s,n),s.prototype.read=function(e,t){this._reads.push({length:Math.abs(e),allowLess:e<0,func:t}),process.nextTick((function(){this._process(),this._paused&&this._reads&&this._reads.length>0&&(this._paused=!1,this.emit("drain"))}).bind(this))},s.prototype.write=function(e,t){let r;return this.writable?(Buffer.isBuffer(e)?r=e:r=Buffer.from(e,t||this._encoding),this._buffers.push(r),this._buffered+=r.length,this._process(),this._reads&&0===this._reads.length&&(this._paused=!0),this.writable&&!this._paused):(this.emit("error",Error("Stream not writable")),!1)},s.prototype.end=function(e,t){e&&this.write(e,t),this.writable=!1,this._buffers&&(0===this._buffers.length?this._end():(this._buffers.push(null),this._process()))},s.prototype.destroySoon=s.prototype.end,s.prototype._end=function(){this._reads.length>0&&this.emit("error",Error("Unexpected end of input")),this.destroy()},s.prototype.destroy=function(){this._buffers&&(this.writable=!1,this._reads=null,this._buffers=null,this.emit("close"))},s.prototype._processReadAllowingLess=function(e){this._reads.shift();let t=this._buffers[0];t.length>e.length?(this._buffered-=e.length,this._buffers[0]=t.slice(e.length),e.func.call(this,t.slice(0,e.length))):(this._buffered-=t.length,this._buffers.shift(),e.func.call(this,t))},s.prototype._processRead=function(e){this._reads.shift();let t=0,r=0,i=Buffer.alloc(e.length);for(;t<e.length;){let n=this._buffers[r++],s=Math.min(n.length,e.length-t);n.copy(i,t,0,s),t+=s,s!==n.length&&(this._buffers[--r]=n.slice(s))}r>0&&this._buffers.splice(0,r),this._buffered-=e.length,e.func.call(this,i)},s.prototype._process=function(){try{for(;this._buffered>0&&this._reads&&this._reads.length>0;){let e=this._reads[0];if(e.allowLess)this._processReadAllowingLess(e);else if(this._buffered>=e.length)this._processRead(e);else break}this._buffers&&!this.writable&&this._end()}catch(e){this.emit("error",e)}}},2949:e=>{"use strict";e.exports={PNG_SIGNATURE:[137,80,78,71,13,10,26,10],TYPE_IHDR:1229472850,TYPE_IEND:1229278788,TYPE_IDAT:1229209940,TYPE_PLTE:1347179589,TYPE_tRNS:1951551059,TYPE_gAMA:1732332865,COLORTYPE_GRAYSCALE:0,COLORTYPE_PALETTE:1,COLORTYPE_COLOR:2,COLORTYPE_ALPHA:4,COLORTYPE_PALETTE_COLOR:3,COLORTYPE_COLOR_ALPHA:6,COLORTYPE_TO_BPP_MAP:{0:1,2:3,3:1,4:2,6:4},GAMMA_DIVISION:1e5}},4747:e=>{"use strict";let t=[];!function(){for(let e=0;e<256;e++){let r=e;for(let e=0;e<8;e++)1&r?r=3988292384^r>>>1:r>>>=1;t[e]=r}}();let r=e.exports=function(){this._crc=-1};r.prototype.write=function(e){for(let r=0;r<e.length;r++)this._crc=t[(this._crc^e[r])&255]^this._crc>>>8;return!0},r.prototype.crc32=function(){return -1^this._crc},r.crc32=function(e){let r=-1;for(let i=0;i<e.length;i++)r=t[(r^e[i])&255]^r>>>8;return -1^r}},4734:(e,t,r)=>{"use strict";let i=r(1008),n={0:function(e,t,r,i,n){for(let s=0;s<r;s++)i[n+s]=e[t+s]},1:function(e,t,r,i,n,s){for(let a=0;a<r;a++){let r=a>=s?e[t+a-s]:0,o=e[t+a]-r;i[n+a]=o}},2:function(e,t,r,i,n){for(let s=0;s<r;s++){let a=t>0?e[t+s-r]:0,o=e[t+s]-a;i[n+s]=o}},3:function(e,t,r,i,n,s){for(let a=0;a<r;a++){let o=a>=s?e[t+a-s]:0,l=t>0?e[t+a-r]:0,c=e[t+a]-(o+l>>1);i[n+a]=c}},4:function(e,t,r,n,s,a){for(let o=0;o<r;o++){let l=o>=a?e[t+o-a]:0,c=t>0?e[t+o-r]:0,d=t>0&&o>=a?e[t+o-(r+a)]:0,h=e[t+o]-i(l,c,d);n[s+o]=h}}},s={0:function(e,t,r){let i=0,n=t+r;for(let r=t;r<n;r++)i+=Math.abs(e[r]);return i},1:function(e,t,r,i){let n=0;for(let s=0;s<r;s++){let r=s>=i?e[t+s-i]:0;n+=Math.abs(e[t+s]-r)}return n},2:function(e,t,r){let i=0,n=t+r;for(let s=t;s<n;s++){let n=t>0?e[s-r]:0;i+=Math.abs(e[s]-n)}return i},3:function(e,t,r,i){let n=0;for(let s=0;s<r;s++){let a=s>=i?e[t+s-i]:0,o=t>0?e[t+s-r]:0;n+=Math.abs(e[t+s]-(a+o>>1))}return n},4:function(e,t,r,n){let s=0;for(let a=0;a<r;a++){let o=a>=n?e[t+a-n]:0,l=t>0?e[t+a-r]:0,c=t>0&&a>=n?e[t+a-(r+n)]:0;s+=Math.abs(e[t+a]-i(o,l,c))}return s}};e.exports=function(e,t,r,i,a){let o;if("filterType"in i&&-1!==i.filterType){if("number"==typeof i.filterType)o=[i.filterType];else throw Error("unrecognised filter types")}else o=[0,1,2,3,4];16===i.bitDepth&&(a*=2);let l=t*a,c=0,d=0,h=Buffer.alloc((l+1)*r),u=o[0];for(let t=0;t<r;t++){if(o.length>1){let t=1/0;for(let r=0;r<o.length;r++){let i=s[o[r]](e,d,l,a);i<t&&(u=o[r],t=i)}}h[c]=u,c++,n[u](e,d,l,h,c,a),c+=l,d+=l}return h}},9376:(e,t,r)=>{"use strict";let i=r(1764),n=r(2506),s=r(1569),a=e.exports=function(e){n.call(this);let t=[],r=this;this._filter=new s(e,{read:this.read.bind(this),write:function(e){t.push(e)},complete:function(){r.emit("complete",Buffer.concat(t))}}),this._filter.start()};i.inherits(a,n)},5309:(e,t,r)=>{"use strict";let i=r(3381),n=r(1569);t.process=function(e,t){let r=[],s=new i(e);return new n(t,{read:s.read.bind(s),write:function(e){r.push(e)},complete:function(){}}).start(),s.process(),Buffer.concat(r)}},1569:(e,t,r)=>{"use strict";let i=r(1289),n=r(1008);function s(e,t,r){let i=e*t;return 8!==r&&(i=Math.ceil(i/(8/r))),i}let a=e.exports=function(e,t){let r=e.width,n=e.height,a=e.interlace,o=e.bpp,l=e.depth;if(this.read=t.read,this.write=t.write,this.complete=t.complete,this._imageIndex=0,this._images=[],a){let e=i.getImagePasses(r,n);for(let t=0;t<e.length;t++)this._images.push({byteWidth:s(e[t].width,o,l),height:e[t].height,lineIndex:0})}else this._images.push({byteWidth:s(r,o,l),height:n,lineIndex:0});8===l?this._xComparison=o:16===l?this._xComparison=2*o:this._xComparison=1};a.prototype.start=function(){this.read(this._images[this._imageIndex].byteWidth+1,this._reverseFilterLine.bind(this))},a.prototype._unFilterType1=function(e,t,r){let i=this._xComparison,n=i-1;for(let s=0;s<r;s++){let r=e[1+s],a=s>n?t[s-i]:0;t[s]=r+a}},a.prototype._unFilterType2=function(e,t,r){let i=this._lastLine;for(let n=0;n<r;n++){let r=e[1+n],s=i?i[n]:0;t[n]=r+s}},a.prototype._unFilterType3=function(e,t,r){let i=this._xComparison,n=i-1,s=this._lastLine;for(let a=0;a<r;a++){let r=e[1+a],o=s?s[a]:0,l=Math.floor(((a>n?t[a-i]:0)+o)/2);t[a]=r+l}},a.prototype._unFilterType4=function(e,t,r){let i=this._xComparison,s=i-1,a=this._lastLine;for(let o=0;o<r;o++){let r=e[1+o],l=a?a[o]:0,c=n(o>s?t[o-i]:0,l,o>s&&a?a[o-i]:0);t[o]=r+c}},a.prototype._reverseFilterLine=function(e){let t,r=e[0],i=this._images[this._imageIndex],n=i.byteWidth;if(0===r)t=e.slice(1,n+1);else switch(t=Buffer.alloc(n),r){case 1:this._unFilterType1(e,t,n);break;case 2:this._unFilterType2(e,t,n);break;case 3:this._unFilterType3(e,t,n);break;case 4:this._unFilterType4(e,t,n);break;default:throw Error("Unrecognised filter type - "+r)}this.write(t),i.lineIndex++,i.lineIndex>=i.height?(this._lastLine=null,this._imageIndex++,i=this._images[this._imageIndex]):this._lastLine=t,i?this.read(i.byteWidth+1,this._reverseFilterLine.bind(this)):(this._lastLine=null,this.complete())}},9794:e=>{"use strict";e.exports=function(e,t){let r=t.depth,i=t.width,n=t.height,s=t.colorType,a=t.transColor,o=t.palette,l=e;return 3===s?function(e,t,r,i,n){let s=0;for(let a=0;a<i;a++)for(let i=0;i<r;i++){let r=n[e[s]];if(!r)throw Error("index "+e[s]+" not in palette");for(let e=0;e<4;e++)t[s+e]=r[e];s+=4}}(e,l,i,n,o):(a&&function(e,t,r,i,n){let s=0;for(let a=0;a<i;a++)for(let i=0;i<r;i++){let r=!1;if(1===n.length?n[0]===e[s]&&(r=!0):n[0]===e[s]&&n[1]===e[s+1]&&n[2]===e[s+2]&&(r=!0),r)for(let e=0;e<4;e++)t[s+e]=0;s+=4}}(e,l,i,n,a),8!==r&&(16===r&&(l=Buffer.alloc(i*n*4)),function(e,t,r,i,n){let s=Math.pow(2,n)-1,a=0;for(let n=0;n<i;n++)for(let i=0;i<r;i++){for(let r=0;r<4;r++)t[a+r]=Math.floor(255*e[a+r]/s+.5);a+=4}}(e,l,i,n,r))),l}},1289:(e,t)=>{"use strict";let r=[{x:[0],y:[0]},{x:[4],y:[0]},{x:[0,4],y:[4]},{x:[2,6],y:[0,4]},{x:[0,2,4,6],y:[2,6]},{x:[1,3,5,7],y:[0,2,4,6]},{x:[0,1,2,3,4,5,6,7],y:[1,3,5,7]}];t.getImagePasses=function(e,t){let i=[],n=e%8,s=t%8,a=(e-n)/8,o=(t-s)/8;for(let e=0;e<r.length;e++){let t=r[e],l=a*t.x.length,c=o*t.y.length;for(let e=0;e<t.x.length;e++)if(t.x[e]<n)l++;else break;for(let e=0;e<t.y.length;e++)if(t.y[e]<s)c++;else break;l>0&&c>0&&i.push({width:l,height:c,index:e})}return i},t.getInterlaceIterator=function(e){return function(t,i,n){let s=t%r[n].x.length,a=(t-s)/r[n].x.length*8+r[n].x[s],o=i%r[n].y.length;return 4*a+((i-o)/r[n].y.length*8+r[n].y[o])*e*4}}},2243:(e,t,r)=>{"use strict";let i=r(1764),n=r(6162),s=r(2949),a=r(1274),o=e.exports=function(e){n.call(this),this._packer=new a(e||{}),this._deflate=this._packer.createDeflate(),this.readable=!0};i.inherits(o,n),o.prototype.pack=function(e,t,r,i){this.emit("data",Buffer.from(s.PNG_SIGNATURE)),this.emit("data",this._packer.packIHDR(t,r)),i&&this.emit("data",this._packer.packGAMA(i));let n=this._packer.filterData(e,t,r);this._deflate.on("error",this.emit.bind(this,"error")),this._deflate.on("data",(function(e){this.emit("data",this._packer.packIDAT(e))}).bind(this)),this._deflate.on("end",(function(){this.emit("data",this._packer.packIEND()),this.emit("end")}).bind(this)),this._deflate.end(n)}},100:(e,t,r)=>{"use strict";let i=!0,n=r(1568);n.deflateSync||(i=!1);let s=r(2949),a=r(1274);e.exports=function(e,t){if(!i)throw Error("To use the sync capability of this library in old node versions, please pin pngjs to v2.3.0");let r=new a(t||{}),o=[];o.push(Buffer.from(s.PNG_SIGNATURE)),o.push(r.packIHDR(e.width,e.height)),e.gamma&&o.push(r.packGAMA(e.gamma));let l=r.filterData(e.data,e.width,e.height),c=n.deflateSync(l,r.getDeflateOptions());if(l=null,!c||!c.length)throw Error("bad png - invalid compressed data response");return o.push(r.packIDAT(c)),o.push(r.packIEND()),Buffer.concat(o)}},1274:(e,t,r)=>{"use strict";let i=r(2949),n=r(4747),s=r(3333),a=r(4734),o=r(1568),l=e.exports=function(e){if(this._options=e,e.deflateChunkSize=e.deflateChunkSize||32768,e.deflateLevel=null!=e.deflateLevel?e.deflateLevel:9,e.deflateStrategy=null!=e.deflateStrategy?e.deflateStrategy:3,e.inputHasAlpha=null==e.inputHasAlpha||e.inputHasAlpha,e.deflateFactory=e.deflateFactory||o.createDeflate,e.bitDepth=e.bitDepth||8,e.colorType="number"==typeof e.colorType?e.colorType:i.COLORTYPE_COLOR_ALPHA,e.inputColorType="number"==typeof e.inputColorType?e.inputColorType:i.COLORTYPE_COLOR_ALPHA,-1===[i.COLORTYPE_GRAYSCALE,i.COLORTYPE_COLOR,i.COLORTYPE_COLOR_ALPHA,i.COLORTYPE_ALPHA].indexOf(e.colorType))throw Error("option color type:"+e.colorType+" is not supported at present");if(-1===[i.COLORTYPE_GRAYSCALE,i.COLORTYPE_COLOR,i.COLORTYPE_COLOR_ALPHA,i.COLORTYPE_ALPHA].indexOf(e.inputColorType))throw Error("option input color type:"+e.inputColorType+" is not supported at present");if(8!==e.bitDepth&&16!==e.bitDepth)throw Error("option bit depth:"+e.bitDepth+" is not supported at present")};l.prototype.getDeflateOptions=function(){return{chunkSize:this._options.deflateChunkSize,level:this._options.deflateLevel,strategy:this._options.deflateStrategy}},l.prototype.createDeflate=function(){return this._options.deflateFactory(this.getDeflateOptions())},l.prototype.filterData=function(e,t,r){let n=s(e,t,r,this._options),o=i.COLORTYPE_TO_BPP_MAP[this._options.colorType];return a(n,t,r,this._options,o)},l.prototype._packChunk=function(e,t){let r=t?t.length:0,i=Buffer.alloc(r+12);return i.writeUInt32BE(r,0),i.writeUInt32BE(e,4),t&&t.copy(i,8),i.writeInt32BE(n.crc32(i.slice(4,i.length-4)),i.length-4),i},l.prototype.packGAMA=function(e){let t=Buffer.alloc(4);return t.writeUInt32BE(Math.floor(e*i.GAMMA_DIVISION),0),this._packChunk(i.TYPE_gAMA,t)},l.prototype.packIHDR=function(e,t){let r=Buffer.alloc(13);return r.writeUInt32BE(e,0),r.writeUInt32BE(t,4),r[8]=this._options.bitDepth,r[9]=this._options.colorType,r[10]=0,r[11]=0,r[12]=0,this._packChunk(i.TYPE_IHDR,r)},l.prototype.packIDAT=function(e){return this._packChunk(i.TYPE_IDAT,e)},l.prototype.packIEND=function(){return this._packChunk(i.TYPE_IEND,null)}},1008:e=>{"use strict";e.exports=function(e,t,r){let i=e+t-r,n=Math.abs(i-e),s=Math.abs(i-t),a=Math.abs(i-r);return n<=s&&n<=a?e:s<=a?t:r}},2336:(e,t,r)=>{"use strict";let i=r(1764),n=r(1568),s=r(2506),a=r(9376),o=r(801),l=r(1349),c=r(9794),d=e.exports=function(e){s.call(this),this._parser=new o(e,{read:this.read.bind(this),error:this._handleError.bind(this),metadata:this._handleMetaData.bind(this),gamma:this.emit.bind(this,"gamma"),palette:this._handlePalette.bind(this),transColor:this._handleTransColor.bind(this),finished:this._finished.bind(this),inflateData:this._inflateData.bind(this),simpleTransparency:this._simpleTransparency.bind(this),headersFinished:this._headersFinished.bind(this)}),this._options=e,this.writable=!0,this._parser.start()};i.inherits(d,s),d.prototype._handleError=function(e){this.emit("error",e),this.writable=!1,this.destroy(),this._inflate&&this._inflate.destroy&&this._inflate.destroy(),this._filter&&(this._filter.destroy(),this._filter.on("error",function(){})),this.errord=!0},d.prototype._inflateData=function(e){if(!this._inflate){if(this._bitmapInfo.interlace)this._inflate=n.createInflate(),this._inflate.on("error",this.emit.bind(this,"error")),this._filter.on("complete",this._complete.bind(this)),this._inflate.pipe(this._filter);else{let e=((this._bitmapInfo.width*this._bitmapInfo.bpp*this._bitmapInfo.depth+7>>3)+1)*this._bitmapInfo.height,t=Math.max(e,n.Z_MIN_CHUNK);this._inflate=n.createInflate({chunkSize:t});let r=e,i=this.emit.bind(this,"error");this._inflate.on("error",function(e){r&&i(e)}),this._filter.on("complete",this._complete.bind(this));let s=this._filter.write.bind(this._filter);this._inflate.on("data",function(e){r&&(e.length>r&&(e=e.slice(0,r)),r-=e.length,s(e))}),this._inflate.on("end",this._filter.end.bind(this._filter))}}this._inflate.write(e)},d.prototype._handleMetaData=function(e){this._metaData=e,this._bitmapInfo=Object.create(e),this._filter=new a(this._bitmapInfo)},d.prototype._handleTransColor=function(e){this._bitmapInfo.transColor=e},d.prototype._handlePalette=function(e){this._bitmapInfo.palette=e},d.prototype._simpleTransparency=function(){this._metaData.alpha=!0},d.prototype._headersFinished=function(){this.emit("metadata",this._metaData)},d.prototype._finished=function(){this.errord||(this._inflate?this._inflate.end():this.emit("error","No Inflate block"))},d.prototype._complete=function(e){let t;if(!this.errord){try{let r=l.dataToBitMap(e,this._bitmapInfo);t=c(r,this._bitmapInfo),r=null}catch(e){this._handleError(e);return}this.emit("parsed",t)}}},369:(e,t,r)=>{"use strict";let i=!0,n=r(1568),s=r(1102);n.deflateSync||(i=!1);let a=r(3381),o=r(5309),l=r(801),c=r(1349),d=r(9794);e.exports=function(e,t){let r,h,u,p;if(!i)throw Error("To use the sync capability of this library in old node versions, please pin pngjs to v2.3.0");let f=[],m=new a(e);if(new l(t,{read:m.read.bind(m),error:function(e){r=e},metadata:function(e){h=e},gamma:function(e){u=e},palette:function(e){h.palette=e},transColor:function(e){h.transColor=e},inflateData:function(e){f.push(e)},simpleTransparency:function(){h.alpha=!0}}).start(),m.process(),r)throw r;let g=Buffer.concat(f);if(f.length=0,h.interlace)p=n.inflateSync(g);else{let e=((h.width*h.bpp*h.depth+7>>3)+1)*h.height;p=s(g,{chunkSize:e,maxLength:e})}if(g=null,!p||!p.length)throw Error("bad png - invalid inflate data response");let x=o.process(p,h);g=null;let b=c.dataToBitMap(x,h);x=null;let y=d(b,h);return h.data=y,h.gamma=u||0,h}},801:(e,t,r)=>{"use strict";let i=r(2949),n=r(4747),s=e.exports=function(e,t){this._options=e,e.checkCRC=!1!==e.checkCRC,this._hasIHDR=!1,this._hasIEND=!1,this._emittedHeadersFinished=!1,this._palette=[],this._colorType=0,this._chunks={},this._chunks[i.TYPE_IHDR]=this._handleIHDR.bind(this),this._chunks[i.TYPE_IEND]=this._handleIEND.bind(this),this._chunks[i.TYPE_IDAT]=this._handleIDAT.bind(this),this._chunks[i.TYPE_PLTE]=this._handlePLTE.bind(this),this._chunks[i.TYPE_tRNS]=this._handleTRNS.bind(this),this._chunks[i.TYPE_gAMA]=this._handleGAMA.bind(this),this.read=t.read,this.error=t.error,this.metadata=t.metadata,this.gamma=t.gamma,this.transColor=t.transColor,this.palette=t.palette,this.parsed=t.parsed,this.inflateData=t.inflateData,this.finished=t.finished,this.simpleTransparency=t.simpleTransparency,this.headersFinished=t.headersFinished||function(){}};s.prototype.start=function(){this.read(i.PNG_SIGNATURE.length,this._parseSignature.bind(this))},s.prototype._parseSignature=function(e){let t=i.PNG_SIGNATURE;for(let r=0;r<t.length;r++)if(e[r]!==t[r]){this.error(Error("Invalid file signature"));return}this.read(8,this._parseChunkBegin.bind(this))},s.prototype._parseChunkBegin=function(e){let t=e.readUInt32BE(0),r=e.readUInt32BE(4),s="";for(let t=4;t<8;t++)s+=String.fromCharCode(e[t]);let a=!!(32&e[4]);if(!this._hasIHDR&&r!==i.TYPE_IHDR){this.error(Error("Expected IHDR on beggining"));return}if(this._crc=new n,this._crc.write(Buffer.from(s)),this._chunks[r])return this._chunks[r](t);if(!a){this.error(Error("Unsupported critical chunk type "+s));return}this.read(t+4,this._skipChunk.bind(this))},s.prototype._skipChunk=function(){this.read(8,this._parseChunkBegin.bind(this))},s.prototype._handleChunkEnd=function(){this.read(4,this._parseChunkEnd.bind(this))},s.prototype._parseChunkEnd=function(e){let t=e.readInt32BE(0),r=this._crc.crc32();if(this._options.checkCRC&&r!==t){this.error(Error("Crc error - "+t+" - "+r));return}this._hasIEND||this.read(8,this._parseChunkBegin.bind(this))},s.prototype._handleIHDR=function(e){this.read(e,this._parseIHDR.bind(this))},s.prototype._parseIHDR=function(e){this._crc.write(e);let t=e.readUInt32BE(0),r=e.readUInt32BE(4),n=e[8],s=e[9],a=e[10],o=e[11],l=e[12];if(8!==n&&4!==n&&2!==n&&1!==n&&16!==n){this.error(Error("Unsupported bit depth "+n));return}if(!(s in i.COLORTYPE_TO_BPP_MAP)){this.error(Error("Unsupported color type"));return}if(0!==a){this.error(Error("Unsupported compression method"));return}if(0!==o){this.error(Error("Unsupported filter method"));return}if(0!==l&&1!==l){this.error(Error("Unsupported interlace method"));return}this._colorType=s;let c=i.COLORTYPE_TO_BPP_MAP[this._colorType];this._hasIHDR=!0,this.metadata({width:t,height:r,depth:n,interlace:!!l,palette:!!(s&i.COLORTYPE_PALETTE),color:!!(s&i.COLORTYPE_COLOR),alpha:!!(s&i.COLORTYPE_ALPHA),bpp:c,colorType:s}),this._handleChunkEnd()},s.prototype._handlePLTE=function(e){this.read(e,this._parsePLTE.bind(this))},s.prototype._parsePLTE=function(e){this._crc.write(e);let t=Math.floor(e.length/3);for(let r=0;r<t;r++)this._palette.push([e[3*r],e[3*r+1],e[3*r+2],255]);this.palette(this._palette),this._handleChunkEnd()},s.prototype._handleTRNS=function(e){this.simpleTransparency(),this.read(e,this._parseTRNS.bind(this))},s.prototype._parseTRNS=function(e){if(this._crc.write(e),this._colorType===i.COLORTYPE_PALETTE_COLOR){if(0===this._palette.length){this.error(Error("Transparency chunk must be after palette"));return}if(e.length>this._palette.length){this.error(Error("More transparent colors than palette size"));return}for(let t=0;t<e.length;t++)this._palette[t][3]=e[t];this.palette(this._palette)}this._colorType===i.COLORTYPE_GRAYSCALE&&this.transColor([e.readUInt16BE(0)]),this._colorType===i.COLORTYPE_COLOR&&this.transColor([e.readUInt16BE(0),e.readUInt16BE(2),e.readUInt16BE(4)]),this._handleChunkEnd()},s.prototype._handleGAMA=function(e){this.read(e,this._parseGAMA.bind(this))},s.prototype._parseGAMA=function(e){this._crc.write(e),this.gamma(e.readUInt32BE(0)/i.GAMMA_DIVISION),this._handleChunkEnd()},s.prototype._handleIDAT=function(e){this._emittedHeadersFinished||(this._emittedHeadersFinished=!0,this.headersFinished()),this.read(-e,this._parseIDAT.bind(this,e))},s.prototype._parseIDAT=function(e,t){if(this._crc.write(t),this._colorType===i.COLORTYPE_PALETTE_COLOR&&0===this._palette.length)throw Error("Expected palette not found");this.inflateData(t);let r=e-t.length;r>0?this._handleIDAT(r):this._handleChunkEnd()},s.prototype._handleIEND=function(e){this.read(e,this._parseIEND.bind(this))},s.prototype._parseIEND=function(e){this._crc.write(e),this._hasIEND=!0,this._handleChunkEnd(),this.finished&&this.finished()}},438:(e,t,r)=>{"use strict";let i=r(369),n=r(100);t.read=function(e,t){return i(e,t||{})},t.write=function(e,t){return n(e,t)}},1687:(e,t,r)=>{"use strict";let i=r(1764),n=r(6162),s=r(2336),a=r(2243),o=r(438),l=t.y=function(e){n.call(this),e=e||{},this.width=0|e.width,this.height=0|e.height,this.data=this.width>0&&this.height>0?Buffer.alloc(4*this.width*this.height):null,e.fill&&this.data&&this.data.fill(0),this.gamma=0,this.readable=this.writable=!0,this._parser=new s(e),this._parser.on("error",this.emit.bind(this,"error")),this._parser.on("close",this._handleClose.bind(this)),this._parser.on("metadata",this._metadata.bind(this)),this._parser.on("gamma",this._gamma.bind(this)),this._parser.on("parsed",(function(e){this.data=e,this.emit("parsed",e)}).bind(this)),this._packer=new a(e),this._packer.on("data",this.emit.bind(this,"data")),this._packer.on("end",this.emit.bind(this,"end")),this._parser.on("close",this._handleClose.bind(this)),this._packer.on("error",this.emit.bind(this,"error"))};i.inherits(l,n),l.sync=o,l.prototype.pack=function(){return this.data&&this.data.length?process.nextTick((function(){this._packer.pack(this.data,this.width,this.height,this.gamma)}).bind(this)):this.emit("error","No data provided"),this},l.prototype.parse=function(e,t){if(t){let e,r;e=(function(e){this.removeListener("error",r),this.data=e,t(null,this)}).bind(this),r=(function(r){this.removeListener("parsed",e),t(r,null)}).bind(this),this.once("parsed",e),this.once("error",r)}return this.end(e),this},l.prototype.write=function(e){return this._parser.write(e),!0},l.prototype.end=function(e){this._parser.end(e)},l.prototype._metadata=function(e){this.width=e.width,this.height=e.height,this.emit("metadata",e)},l.prototype._gamma=function(e){this.gamma=e},l.prototype._handleClose=function(){this._parser.writable||this._packer.readable||this.emit("close")},l.bitblt=function(e,t,r,i,n,s,a,o){if(i|=0,n|=0,s|=0,a|=0,o|=0,(r|=0)>e.width||i>e.height||r+n>e.width||i+s>e.height)throw Error("bitblt reading outside image");if(a>t.width||o>t.height||a+n>t.width||o+s>t.height)throw Error("bitblt writing outside image");for(let l=0;l<s;l++)e.data.copy(t.data,(o+l)*t.width+a<<2,(i+l)*e.width+r<<2,(i+l)*e.width+r+n<<2)},l.prototype.bitblt=function(e,t,r,i,n,s,a){return l.bitblt(this,e,t,r,i,n,s,a),this},l.adjustGamma=function(e){if(e.gamma){for(let t=0;t<e.height;t++)for(let r=0;r<e.width;r++){let i=e.width*t+r<<2;for(let t=0;t<3;t++){let r=e.data[i+t]/255;r=Math.pow(r,1/2.2/e.gamma),e.data[i+t]=Math.round(255*r)}}e.gamma=0}},l.prototype.adjustGamma=function(){l.adjustGamma(this)}},1102:(e,t,r)=>{"use strict";let i=r(7790).ok,n=r(1568),s=r(1764),a=r(8893).kMaxLength;function o(e){if(!(this instanceof o))return new o(e);e&&e.chunkSize<n.Z_MIN_CHUNK&&(e.chunkSize=n.Z_MIN_CHUNK),n.Inflate.call(this,e),this._offset=void 0===this._offset?this._outOffset:this._offset,this._buffer=this._buffer||this._outBuffer,e&&null!=e.maxLength&&(this._maxLength=e.maxLength)}function l(e,t){t&&process.nextTick(t),e._handle&&(e._handle.close(),e._handle=null)}function c(e,t){return function(e,t){if("string"==typeof t&&(t=Buffer.from(t)),!(t instanceof Buffer))throw TypeError("Not a string or buffer");let r=e._finishFlushFlag;return null==r&&(r=n.Z_FINISH),e._processChunk(t,r)}(new o(t),e)}o.prototype._processChunk=function(e,t,r){let s,o;if("function"==typeof r)return n.Inflate._processChunk.call(this,e,t,r);let c=this,d=e&&e.length,h=this._chunkSize-this._offset,u=this._maxLength,p=0,f=[],m=0;this.on("error",function(e){s=e}),i(this._handle,"zlib binding closed");do o=(o=this._handle.writeSync(t,e,p,d,this._buffer,this._offset,h))||this._writeState;while(!this._hadError&&function(e,t){if(c._hadError)return;let r=h-t;if(i(r>=0,"have should not go down"),r>0){let e=c._buffer.slice(c._offset,c._offset+r);if(c._offset+=r,e.length>u&&(e=e.slice(0,u)),f.push(e),m+=e.length,0==(u-=e.length))return!1}return(0===t||c._offset>=c._chunkSize)&&(h=c._chunkSize,c._offset=0,c._buffer=Buffer.allocUnsafe(c._chunkSize)),0===t&&(p+=d-e,d=e,!0)}(o[0],o[1]));if(this._hadError)throw s;if(m>=a)throw l(this),RangeError("Cannot create final Buffer. It would be larger than 0x"+a.toString(16)+" bytes");let g=Buffer.concat(f,m);return l(this),g},s.inherits(o,n.Inflate),e.exports=t=c,t.Inflate=o,t.createInflate=function(e){return new o(e)},t.inflateSync=c},3381:e=>{"use strict";let t=e.exports=function(e){this._buffer=e,this._reads=[]};t.prototype.read=function(e,t){this._reads.push({length:Math.abs(e),allowLess:e<0,func:t})},t.prototype.process=function(){for(;this._reads.length>0&&this._buffer.length;){let e=this._reads[0];if(this._buffer.length&&(this._buffer.length>=e.length||e.allowLess)){this._reads.shift();let t=this._buffer;this._buffer=t.slice(e.length),e.func.call(this,t.slice(0,e.length))}else break}return this._reads.length>0?Error("There are some read requests waitng on finished stream"):this._buffer.length>0?Error("unrecognised content at end of stream"):void 0}},9157:(e,t,r)=>{let i=r(1817),n=r(8895),s=r(3162),a=r(975);function o(e,t,r,s,a){let o=[].slice.call(arguments,1),l=o.length,c="function"==typeof o[l-1];if(!c&&!i())throw Error("Callback required as last argument");if(c){if(l<2)throw Error("Too few arguments provided");2===l?(a=r,r=t,t=s=void 0):3===l&&(t.getContext&&void 0===a?(a=s,s=void 0):(a=s,s=r,r=t,t=void 0))}else{if(l<1)throw Error("Too few arguments provided");return 1===l?(r=t,t=s=void 0):2!==l||t.getContext||(s=r,r=t,t=void 0),new Promise(function(i,a){try{let a=n.create(r,s);i(e(a,t,s))}catch(e){a(e)}})}try{let i=n.create(r,s);a(null,e(i,t,s))}catch(e){a(e)}}n.create,t.toCanvas=o.bind(null,s.render),o.bind(null,s.renderToDataURL),o.bind(null,function(e,t,r){return a.render(e,r)})},1817:e=>{e.exports=function(){return"function"==typeof Promise&&Promise.prototype&&Promise.prototype.then}},731:(e,t,r)=>{let i=r(2540).getSymbolSize;t.getRowColCoords=function(e){if(1===e)return[];let t=Math.floor(e/7)+2,r=i(e),n=145===r?26:2*Math.ceil((r-13)/(2*t-2)),s=[r-7];for(let e=1;e<t-1;e++)s[e]=s[e-1]-n;return s.push(6),s.reverse()},t.getPositions=function(e){let r=[],i=t.getRowColCoords(e),n=i.length;for(let e=0;e<n;e++)for(let t=0;t<n;t++)(0!==e||0!==t)&&(0!==e||t!==n-1)&&(e!==n-1||0!==t)&&r.push([i[e],i[t]]);return r}},2307:(e,t,r)=>{let i=r(7798),n=["0","1","2","3","4","5","6","7","8","9","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"," ","$","%","*","+","-",".","/",":"];function s(e){this.mode=i.ALPHANUMERIC,this.data=e}s.getBitsLength=function(e){return 11*Math.floor(e/2)+e%2*6},s.prototype.getLength=function(){return this.data.length},s.prototype.getBitsLength=function(){return s.getBitsLength(this.data.length)},s.prototype.write=function(e){let t;for(t=0;t+2<=this.data.length;t+=2){let r=45*n.indexOf(this.data[t]);r+=n.indexOf(this.data[t+1]),e.put(r,11)}this.data.length%2&&e.put(n.indexOf(this.data[t]),6)},e.exports=s},6730:e=>{function t(){this.buffer=[],this.length=0}t.prototype={get:function(e){return(this.buffer[Math.floor(e/8)]>>>7-e%8&1)==1},put:function(e,t){for(let r=0;r<t;r++)this.putBit((e>>>t-r-1&1)==1)},getLengthInBits:function(){return this.length},putBit:function(e){let t=Math.floor(this.length/8);this.buffer.length<=t&&this.buffer.push(0),e&&(this.buffer[t]|=128>>>this.length%8),this.length++}},e.exports=t},4638:e=>{function t(e){if(!e||e<1)throw Error("BitMatrix size must be defined and greater than 0");this.size=e,this.data=new Uint8Array(e*e),this.reservedBit=new Uint8Array(e*e)}t.prototype.set=function(e,t,r,i){let n=e*this.size+t;this.data[n]=r,i&&(this.reservedBit[n]=!0)},t.prototype.get=function(e,t){return this.data[e*this.size+t]},t.prototype.xor=function(e,t,r){this.data[e*this.size+t]^=r},t.prototype.isReserved=function(e,t){return this.reservedBit[e*this.size+t]},e.exports=t},592:(e,t,r)=>{let i=r(7798);function n(e){this.mode=i.BYTE,"string"==typeof e?this.data=new TextEncoder().encode(e):this.data=new Uint8Array(e)}n.getBitsLength=function(e){return 8*e},n.prototype.getLength=function(){return this.data.length},n.prototype.getBitsLength=function(){return n.getBitsLength(this.data.length)},n.prototype.write=function(e){for(let t=0,r=this.data.length;t<r;t++)e.put(this.data[t],8)},e.exports=n},4134:(e,t,r)=>{let i=r(8431),n=[1,1,1,1,1,1,1,1,1,1,2,2,1,2,2,4,1,2,4,4,2,4,4,4,2,4,6,5,2,4,6,6,2,5,8,8,4,5,8,8,4,5,8,11,4,8,10,11,4,9,12,16,4,9,16,16,6,10,12,18,6,10,17,16,6,11,16,19,6,13,18,21,7,14,21,25,8,16,20,25,8,17,23,25,9,17,23,34,9,18,25,30,10,20,27,32,12,21,29,35,12,23,34,37,12,25,34,40,13,26,35,42,14,28,38,45,15,29,40,48,16,31,43,51,17,33,45,54,18,35,48,57,19,37,51,60,19,38,53,63,20,40,56,66,21,43,59,70,22,45,62,74,24,47,65,77,25,49,68,81],s=[7,10,13,17,10,16,22,28,15,26,36,44,20,36,52,64,26,48,72,88,36,64,96,112,40,72,108,130,48,88,132,156,60,110,160,192,72,130,192,224,80,150,224,264,96,176,260,308,104,198,288,352,120,216,320,384,132,240,360,432,144,280,408,480,168,308,448,532,180,338,504,588,196,364,546,650,224,416,600,700,224,442,644,750,252,476,690,816,270,504,750,900,300,560,810,960,312,588,870,1050,336,644,952,1110,360,700,1020,1200,390,728,1050,1260,420,784,1140,1350,450,812,1200,1440,480,868,1290,1530,510,924,1350,1620,540,980,1440,1710,570,1036,1530,1800,570,1064,1590,1890,600,1120,1680,1980,630,1204,1770,2100,660,1260,1860,2220,720,1316,1950,2310,750,1372,2040,2430];t.getBlocksCount=function(e,t){switch(t){case i.L:return n[(e-1)*4+0];case i.M:return n[(e-1)*4+1];case i.Q:return n[(e-1)*4+2];case i.H:return n[(e-1)*4+3];default:return}},t.getTotalCodewordsCount=function(e,t){switch(t){case i.L:return s[(e-1)*4+0];case i.M:return s[(e-1)*4+1];case i.Q:return s[(e-1)*4+2];case i.H:return s[(e-1)*4+3];default:return}}},8431:(e,t)=>{t.L={bit:1},t.M={bit:0},t.Q={bit:3},t.H={bit:2},t.isValid=function(e){return e&&void 0!==e.bit&&e.bit>=0&&e.bit<4},t.from=function(e,r){if(t.isValid(e))return e;try{return function(e){if("string"!=typeof e)throw Error("Param is not a string");switch(e.toLowerCase()){case"l":case"low":return t.L;case"m":case"medium":return t.M;case"q":case"quartile":return t.Q;case"h":case"high":return t.H;default:throw Error("Unknown EC Level: "+e)}}(e)}catch(e){return r}}},7422:(e,t,r)=>{let i=r(2540).getSymbolSize;t.getPositions=function(e){let t=i(e);return[[0,0],[t-7,0],[0,t-7]]}},6445:(e,t,r)=>{let i=r(2540),n=i.getBCHDigit(1335);t.getEncodedBits=function(e,t){let r=e.bit<<3|t,s=r<<10;for(;i.getBCHDigit(s)-n>=0;)s^=1335<<i.getBCHDigit(s)-n;return(r<<10|s)^21522}},2959:(e,t)=>{let r=new Uint8Array(512),i=new Uint8Array(256);(function(){let e=1;for(let t=0;t<255;t++)r[t]=e,i[e]=t,256&(e<<=1)&&(e^=285);for(let e=255;e<512;e++)r[e]=r[e-255]})(),t.log=function(e){if(e<1)throw Error("log("+e+")");return i[e]},t.exp=function(e){return r[e]},t.mul=function(e,t){return 0===e||0===t?0:r[i[e]+i[t]]}},3616:(e,t,r)=>{let i=r(7798),n=r(2540);function s(e){this.mode=i.KANJI,this.data=e}s.getBitsLength=function(e){return 13*e},s.prototype.getLength=function(){return this.data.length},s.prototype.getBitsLength=function(){return s.getBitsLength(this.data.length)},s.prototype.write=function(e){let t;for(t=0;t<this.data.length;t++){let r=n.toSJIS(this.data[t]);if(r>=33088&&r<=40956)r-=33088;else if(r>=57408&&r<=60351)r-=49472;else throw Error("Invalid SJIS character: "+this.data[t]+"\nMake sure your charset is UTF-8");r=(r>>>8&255)*192+(255&r),e.put(r,13)}},e.exports=s},6754:(e,t)=>{t.Patterns={PATTERN000:0,PATTERN001:1,PATTERN010:2,PATTERN011:3,PATTERN100:4,PATTERN101:5,PATTERN110:6,PATTERN111:7};let r={N1:3,N2:3,N3:40,N4:10};t.isValid=function(e){return null!=e&&""!==e&&!isNaN(e)&&e>=0&&e<=7},t.from=function(e){return t.isValid(e)?parseInt(e,10):void 0},t.getPenaltyN1=function(e){let t=e.size,i=0,n=0,s=0,a=null,o=null;for(let l=0;l<t;l++){n=s=0,a=o=null;for(let c=0;c<t;c++){let t=e.get(l,c);t===a?n++:(n>=5&&(i+=r.N1+(n-5)),a=t,n=1),(t=e.get(c,l))===o?s++:(s>=5&&(i+=r.N1+(s-5)),o=t,s=1)}n>=5&&(i+=r.N1+(n-5)),s>=5&&(i+=r.N1+(s-5))}return i},t.getPenaltyN2=function(e){let t=e.size,i=0;for(let r=0;r<t-1;r++)for(let n=0;n<t-1;n++){let t=e.get(r,n)+e.get(r,n+1)+e.get(r+1,n)+e.get(r+1,n+1);(4===t||0===t)&&i++}return i*r.N2},t.getPenaltyN3=function(e){let t=e.size,i=0,n=0,s=0;for(let r=0;r<t;r++){n=s=0;for(let a=0;a<t;a++)n=n<<1&2047|e.get(r,a),a>=10&&(1488===n||93===n)&&i++,s=s<<1&2047|e.get(a,r),a>=10&&(1488===s||93===s)&&i++}return i*r.N3},t.getPenaltyN4=function(e){let t=0,i=e.data.length;for(let r=0;r<i;r++)t+=e.data[r];return Math.abs(Math.ceil(100*t/i/5)-10)*r.N4},t.applyMask=function(e,r){let i=r.size;for(let n=0;n<i;n++)for(let s=0;s<i;s++)r.isReserved(s,n)||r.xor(s,n,function(e,r,i){switch(e){case t.Patterns.PATTERN000:return(r+i)%2==0;case t.Patterns.PATTERN001:return r%2==0;case t.Patterns.PATTERN010:return i%3==0;case t.Patterns.PATTERN011:return(r+i)%3==0;case t.Patterns.PATTERN100:return(Math.floor(r/2)+Math.floor(i/3))%2==0;case t.Patterns.PATTERN101:return r*i%2+r*i%3==0;case t.Patterns.PATTERN110:return(r*i%2+r*i%3)%2==0;case t.Patterns.PATTERN111:return(r*i%3+(r+i)%2)%2==0;default:throw Error("bad maskPattern:"+e)}}(e,s,n))},t.getBestMask=function(e,r){let i=Object.keys(t.Patterns).length,n=0,s=1/0;for(let a=0;a<i;a++){r(a),t.applyMask(a,e);let i=t.getPenaltyN1(e)+t.getPenaltyN2(e)+t.getPenaltyN3(e)+t.getPenaltyN4(e);t.applyMask(a,e),i<s&&(s=i,n=a)}return n}},7798:(e,t,r)=>{let i=r(8517),n=r(232);t.NUMERIC={id:"Numeric",bit:1,ccBits:[10,12,14]},t.ALPHANUMERIC={id:"Alphanumeric",bit:2,ccBits:[9,11,13]},t.BYTE={id:"Byte",bit:4,ccBits:[8,16,16]},t.KANJI={id:"Kanji",bit:8,ccBits:[8,10,12]},t.MIXED={bit:-1},t.getCharCountIndicator=function(e,t){if(!e.ccBits)throw Error("Invalid mode: "+e);if(!i.isValid(t))throw Error("Invalid version: "+t);return t>=1&&t<10?e.ccBits[0]:t<27?e.ccBits[1]:e.ccBits[2]},t.getBestModeForData=function(e){return n.testNumeric(e)?t.NUMERIC:n.testAlphanumeric(e)?t.ALPHANUMERIC:n.testKanji(e)?t.KANJI:t.BYTE},t.toString=function(e){if(e&&e.id)return e.id;throw Error("Invalid mode")},t.isValid=function(e){return e&&e.bit&&e.ccBits},t.from=function(e,r){if(t.isValid(e))return e;try{return function(e){if("string"!=typeof e)throw Error("Param is not a string");switch(e.toLowerCase()){case"numeric":return t.NUMERIC;case"alphanumeric":return t.ALPHANUMERIC;case"kanji":return t.KANJI;case"byte":return t.BYTE;default:throw Error("Unknown mode: "+e)}}(e)}catch(e){return r}}},4790:(e,t,r)=>{let i=r(7798);function n(e){this.mode=i.NUMERIC,this.data=e.toString()}n.getBitsLength=function(e){return 10*Math.floor(e/3)+(e%3?e%3*3+1:0)},n.prototype.getLength=function(){return this.data.length},n.prototype.getBitsLength=function(){return n.getBitsLength(this.data.length)},n.prototype.write=function(e){let t,r;for(t=0;t+3<=this.data.length;t+=3)r=parseInt(this.data.substr(t,3),10),e.put(r,10);let i=this.data.length-t;i>0&&(r=parseInt(this.data.substr(t),10),e.put(r,3*i+1))},e.exports=n},6686:(e,t,r)=>{let i=r(2959);t.mul=function(e,t){let r=new Uint8Array(e.length+t.length-1);for(let n=0;n<e.length;n++)for(let s=0;s<t.length;s++)r[n+s]^=i.mul(e[n],t[s]);return r},t.mod=function(e,t){let r=new Uint8Array(e);for(;r.length-t.length>=0;){let e=r[0];for(let n=0;n<t.length;n++)r[n]^=i.mul(t[n],e);let n=0;for(;n<r.length&&0===r[n];)n++;r=r.slice(n)}return r},t.generateECPolynomial=function(e){let r=new Uint8Array([1]);for(let n=0;n<e;n++)r=t.mul(r,new Uint8Array([1,i.exp(n)]));return r}},8895:(e,t,r)=>{let i=r(2540),n=r(8431),s=r(6730),a=r(4638),o=r(731),l=r(7422),c=r(6754),d=r(4134),h=r(9444),u=r(5463),p=r(6445),f=r(7798),m=r(6485);function g(e,t,r){let i,n;let s=e.size,a=p.getEncodedBits(t,r);for(i=0;i<15;i++)n=(a>>i&1)==1,i<6?e.set(i,8,n,!0):i<8?e.set(i+1,8,n,!0):e.set(s-15+i,8,n,!0),i<8?e.set(8,s-i-1,n,!0):i<9?e.set(8,15-i-1+1,n,!0):e.set(8,15-i-1,n,!0);e.set(s-8,8,1,!0)}t.create=function(e,t){let r,p;if(void 0===e||""===e)throw Error("No input text");let x=n.M;return void 0!==t&&(x=n.from(t.errorCorrectionLevel,n.M),r=u.from(t.version),p=c.from(t.maskPattern),t.toSJISFunc&&i.setToSJISFunction(t.toSJISFunc)),function(e,t,r,n){let p;if(Array.isArray(e))p=m.fromArray(e);else if("string"==typeof e){let i=t;if(!i){let t=m.rawSplit(e);i=u.getBestVersionForData(t,r)}p=m.fromString(e,i||40)}else throw Error("Invalid data");let x=u.getBestVersionForData(p,r);if(!x)throw Error("The amount of data is too big to be stored in a QR Code");if(t){if(t<x)throw Error("\nThe chosen QR Code version cannot contain this amount of data.\nMinimum version required to store current data is: "+x+".\n")}else t=x;let b=function(e,t,r){let n=new s;r.forEach(function(t){n.put(t.mode.bit,4),n.put(t.getLength(),f.getCharCountIndicator(t.mode,e)),t.write(n)});let a=(i.getSymbolTotalCodewords(e)-d.getTotalCodewordsCount(e,t))*8;for(n.getLengthInBits()+4<=a&&n.put(0,4);n.getLengthInBits()%8!=0;)n.putBit(0);let o=(a-n.getLengthInBits())/8;for(let e=0;e<o;e++)n.put(e%2?17:236,8);return function(e,t,r){let n,s;let a=i.getSymbolTotalCodewords(t),o=a-d.getTotalCodewordsCount(t,r),l=d.getBlocksCount(t,r),c=a%l,u=l-c,p=Math.floor(a/l),f=Math.floor(o/l),m=f+1,g=p-f,x=new h(g),b=0,y=Array(l),w=Array(l),_=0,v=new Uint8Array(e.buffer);for(let e=0;e<l;e++){let t=e<u?f:m;y[e]=v.slice(b,b+t),w[e]=x.encode(y[e]),b+=t,_=Math.max(_,t)}let k=new Uint8Array(a),N=0;for(n=0;n<_;n++)for(s=0;s<l;s++)n<y[s].length&&(k[N++]=y[s][n]);for(n=0;n<g;n++)for(s=0;s<l;s++)k[N++]=w[s][n];return k}(n,e,t)}(t,r,p),y=new a(i.getSymbolSize(t));return function(e,t){let r=e.size,i=l.getPositions(t);for(let t=0;t<i.length;t++){let n=i[t][0],s=i[t][1];for(let t=-1;t<=7;t++)if(!(n+t<=-1)&&!(r<=n+t))for(let i=-1;i<=7;i++)s+i<=-1||r<=s+i||(t>=0&&t<=6&&(0===i||6===i)||i>=0&&i<=6&&(0===t||6===t)||t>=2&&t<=4&&i>=2&&i<=4?e.set(n+t,s+i,!0,!0):e.set(n+t,s+i,!1,!0))}}(y,t),function(e){let t=e.size;for(let r=8;r<t-8;r++){let t=r%2==0;e.set(r,6,t,!0),e.set(6,r,t,!0)}}(y),function(e,t){let r=o.getPositions(t);for(let t=0;t<r.length;t++){let i=r[t][0],n=r[t][1];for(let t=-2;t<=2;t++)for(let r=-2;r<=2;r++)-2===t||2===t||-2===r||2===r||0===t&&0===r?e.set(i+t,n+r,!0,!0):e.set(i+t,n+r,!1,!0)}}(y,t),g(y,r,0),t>=7&&function(e,t){let r,i,n;let s=e.size,a=u.getEncodedBits(t);for(let t=0;t<18;t++)r=Math.floor(t/3),i=t%3+s-8-3,n=(a>>t&1)==1,e.set(r,i,n,!0),e.set(i,r,n,!0)}(y,t),function(e,t){let r=e.size,i=-1,n=r-1,s=7,a=0;for(let o=r-1;o>0;o-=2)for(6===o&&o--;;){for(let r=0;r<2;r++)if(!e.isReserved(n,o-r)){let i=!1;a<t.length&&(i=(t[a]>>>s&1)==1),e.set(n,o-r,i),-1==--s&&(a++,s=7)}if((n+=i)<0||r<=n){n-=i,i=-i;break}}}(y,b),isNaN(n)&&(n=c.getBestMask(y,g.bind(null,y,r))),c.applyMask(n,y),g(y,r,n),{modules:y,version:t,errorCorrectionLevel:r,maskPattern:n,segments:p}}(e,r,x,p)}},9444:(e,t,r)=>{let i=r(6686);function n(e){this.genPoly=void 0,this.degree=e,this.degree&&this.initialize(this.degree)}n.prototype.initialize=function(e){this.degree=e,this.genPoly=i.generateECPolynomial(this.degree)},n.prototype.encode=function(e){if(!this.genPoly)throw Error("Encoder not initialized");let t=new Uint8Array(e.length+this.degree);t.set(e);let r=i.mod(t,this.genPoly),n=this.degree-r.length;if(n>0){let e=new Uint8Array(this.degree);return e.set(r,n),e}return r},e.exports=n},232:(e,t)=>{let r="[0-9]+",i="(?:[u3000-u303F]|[u3040-u309F]|[u30A0-u30FF]|[uFF00-uFFEF]|[u4E00-u9FAF]|[u2605-u2606]|[u2190-u2195]|u203B|[u2010u2015u2018u2019u2025u2026u201Cu201Du2225u2260]|[u0391-u0451]|[u00A7u00A8u00B1u00B4u00D7u00F7])+",n="(?:(?![A-Z0-9 $%*+\\-./:]|"+(i=i.replace(/u/g,"\\u"))+")(?:.|[\r\n]))+";t.KANJI=RegExp(i,"g"),t.BYTE_KANJI=RegExp("[^A-Z0-9 $%*+\\-./:]+","g"),t.BYTE=RegExp(n,"g"),t.NUMERIC=RegExp(r,"g"),t.ALPHANUMERIC=RegExp("[A-Z $%*+\\-./:]+","g");let s=RegExp("^"+i+"$"),a=RegExp("^"+r+"$"),o=RegExp("^[A-Z0-9 $%*+\\-./:]+$");t.testKanji=function(e){return s.test(e)},t.testNumeric=function(e){return a.test(e)},t.testAlphanumeric=function(e){return o.test(e)}},6485:(e,t,r)=>{let i=r(7798),n=r(4790),s=r(2307),a=r(592),o=r(3616),l=r(232),c=r(2540),d=r(4414);function h(e){return unescape(encodeURIComponent(e)).length}function u(e,t,r){let i;let n=[];for(;null!==(i=e.exec(r));)n.push({data:i[0],index:i.index,mode:t,length:i[0].length});return n}function p(e){let t,r;let n=u(l.NUMERIC,i.NUMERIC,e),s=u(l.ALPHANUMERIC,i.ALPHANUMERIC,e);return c.isKanjiModeEnabled()?(t=u(l.BYTE,i.BYTE,e),r=u(l.KANJI,i.KANJI,e)):(t=u(l.BYTE_KANJI,i.BYTE,e),r=[]),n.concat(s,t,r).sort(function(e,t){return e.index-t.index}).map(function(e){return{data:e.data,mode:e.mode,length:e.length}})}function f(e,t){switch(t){case i.NUMERIC:return n.getBitsLength(e);case i.ALPHANUMERIC:return s.getBitsLength(e);case i.KANJI:return o.getBitsLength(e);case i.BYTE:return a.getBitsLength(e)}}function m(e,t){let r;let l=i.getBestModeForData(e);if((r=i.from(t,l))!==i.BYTE&&r.bit<l.bit)throw Error('"'+e+'" cannot be encoded with mode '+i.toString(r)+".\n Suggested mode is: "+i.toString(l));switch(r!==i.KANJI||c.isKanjiModeEnabled()||(r=i.BYTE),r){case i.NUMERIC:return new n(e);case i.ALPHANUMERIC:return new s(e);case i.KANJI:return new o(e);case i.BYTE:return new a(e)}}t.fromArray=function(e){return e.reduce(function(e,t){return"string"==typeof t?e.push(m(t,null)):t.data&&e.push(m(t.data,t.mode)),e},[])},t.fromString=function(e,r){let n=function(e,t){let r={},n={start:{}},s=["start"];for(let a=0;a<e.length;a++){let o=e[a],l=[];for(let e=0;e<o.length;e++){let c=o[e],d=""+a+e;l.push(d),r[d]={node:c,lastCount:0},n[d]={};for(let e=0;e<s.length;e++){let a=s[e];r[a]&&r[a].node.mode===c.mode?(n[a][d]=f(r[a].lastCount+c.length,c.mode)-f(r[a].lastCount,c.mode),r[a].lastCount+=c.length):(r[a]&&(r[a].lastCount=c.length),n[a][d]=f(c.length,c.mode)+4+i.getCharCountIndicator(c.mode,t))}}s=l}for(let e=0;e<s.length;e++)n[s[e]].end=0;return{map:n,table:r}}(function(e){let t=[];for(let r=0;r<e.length;r++){let n=e[r];switch(n.mode){case i.NUMERIC:t.push([n,{data:n.data,mode:i.ALPHANUMERIC,length:n.length},{data:n.data,mode:i.BYTE,length:n.length}]);break;case i.ALPHANUMERIC:t.push([n,{data:n.data,mode:i.BYTE,length:n.length}]);break;case i.KANJI:t.push([n,{data:n.data,mode:i.BYTE,length:h(n.data)}]);break;case i.BYTE:t.push([{data:n.data,mode:i.BYTE,length:h(n.data)}])}}return t}(p(e,c.isKanjiModeEnabled())),r),s=d.find_path(n.map,"start","end"),a=[];for(let e=1;e<s.length-1;e++)a.push(n.table[s[e]].node);return t.fromArray(a.reduce(function(e,t){let r=e.length-1>=0?e[e.length-1]:null;return r&&r.mode===t.mode?e[e.length-1].data+=t.data:e.push(t),e},[]))},t.rawSplit=function(e){return t.fromArray(p(e,c.isKanjiModeEnabled()))}},2540:(e,t)=>{let r;let i=[0,26,44,70,100,134,172,196,242,292,346,404,466,532,581,655,733,815,901,991,1085,1156,1258,1364,1474,1588,1706,1828,1921,2051,2185,2323,2465,2611,2761,2876,3034,3196,3362,3532,3706];t.getSymbolSize=function(e){if(!e)throw Error('"version" cannot be null or undefined');if(e<1||e>40)throw Error('"version" should be in range from 1 to 40');return 4*e+17},t.getSymbolTotalCodewords=function(e){return i[e]},t.getBCHDigit=function(e){let t=0;for(;0!==e;)t++,e>>>=1;return t},t.setToSJISFunction=function(e){if("function"!=typeof e)throw Error('"toSJISFunc" is not a valid function.');r=e},t.isKanjiModeEnabled=function(){return void 0!==r},t.toSJIS=function(e){return r(e)}},8517:(e,t)=>{t.isValid=function(e){return!isNaN(e)&&e>=1&&e<=40}},5463:(e,t,r)=>{let i=r(2540),n=r(4134),s=r(8431),a=r(7798),o=r(8517),l=i.getBCHDigit(7973);function c(e,t){return a.getCharCountIndicator(e,t)+4}t.from=function(e,t){return o.isValid(e)?parseInt(e,10):t},t.getCapacity=function(e,t,r){if(!o.isValid(e))throw Error("Invalid QR Code version");void 0===r&&(r=a.BYTE);let s=(i.getSymbolTotalCodewords(e)-n.getTotalCodewordsCount(e,t))*8;if(r===a.MIXED)return s;let l=s-c(r,e);switch(r){case a.NUMERIC:return Math.floor(l/10*3);case a.ALPHANUMERIC:return Math.floor(l/11*2);case a.KANJI:return Math.floor(l/13);case a.BYTE:default:return Math.floor(l/8)}},t.getBestVersionForData=function(e,r){let i;let n=s.from(r,s.M);if(Array.isArray(e)){if(e.length>1)return function(e,r){for(let i=1;i<=40;i++)if(function(e,t){let r=0;return e.forEach(function(e){let i=c(e.mode,t);r+=i+e.getBitsLength()}),r}(e,i)<=t.getCapacity(i,r,a.MIXED))return i}(e,n);if(0===e.length)return 1;i=e[0]}else i=e;return function(e,r,i){for(let n=1;n<=40;n++)if(r<=t.getCapacity(n,i,e))return n}(i.mode,i.getLength(),n)},t.getEncodedBits=function(e){if(!o.isValid(e)||e<7)throw Error("Invalid QR Code version");let t=e<<12;for(;i.getBCHDigit(t)-l>=0;)t^=7973<<i.getBCHDigit(t)-l;return e<<12|t}},1270:(e,t,r)=>{"use strict";e.exports=r(5570)},3162:(e,t,r)=>{let i=r(802);t.render=function(e,t,r){var n;let s=r,a=t;void 0!==s||t&&t.getContext||(s=t,t=void 0),t||(a=function(){try{return document.createElement("canvas")}catch(e){throw Error("You need to specify a canvas element")}}()),s=i.getOptions(s);let o=i.getImageWidth(e.modules.size,s),l=a.getContext("2d"),c=l.createImageData(o,o);return i.qrToImageData(c.data,e,s),n=a,l.clearRect(0,0,n.width,n.height),n.style||(n.style={}),n.height=o,n.width=o,n.style.height=o+"px",n.style.width=o+"px",l.putImageData(c,0,0),a},t.renderToDataURL=function(e,r,i){let n=i;void 0!==n||r&&r.getContext||(n=r,r=void 0),n||(n={});let s=t.render(e,r,n),a=n.type||"image/png",o=n.rendererOpts||{};return s.toDataURL(a,o.quality)}},8276:(e,t,r)=>{let i=r(2048),n=r(1687).y,s=r(802);t.render=function(e,t){let r=s.getOptions(t),i=r.rendererOpts,a=s.getImageWidth(e.modules.size,r);i.width=a,i.height=a;let o=new n(i);return s.qrToImageData(o.data,e,r),o},t.renderToDataURL=function(e,r,i){void 0===i&&(i=r,r=void 0),t.renderToBuffer(e,r,function(e,t){e&&i(e);let r="data:image/png;base64,";r+=t.toString("base64"),i(null,r)})},t.renderToBuffer=function(e,r,i){void 0===i&&(i=r,r=void 0);let n=t.render(e,r),s=[];n.on("error",i),n.on("data",function(e){s.push(e)}),n.on("end",function(){i(null,Buffer.concat(s))}),n.pack()},t.renderToFile=function(e,r,n,s){void 0===s&&(s=n,n=void 0);let a=!1,o=(...e)=>{a||(a=!0,s.apply(null,e))},l=i.createWriteStream(e);l.on("error",o),l.on("close",o),t.renderToFileStream(l,r,n)},t.renderToFileStream=function(e,r,i){t.render(r,i).pack().pipe(e)}},975:(e,t,r)=>{let i=r(802);function n(e,t){let r=e.a/255,i=t+'="'+e.hex+'"';return r<1?i+" "+t+'-opacity="'+r.toFixed(2).slice(1)+'"':i}function s(e,t,r){let i=e+t;return void 0!==r&&(i+=" "+r),i}t.render=function(e,t,r){let a=i.getOptions(t),o=e.modules.size,l=e.modules.data,c=o+2*a.margin,d=a.color.light.a?"<path "+n(a.color.light,"fill")+' d="M0 0h'+c+"v"+c+'H0z"/>':"",h="<path "+n(a.color.dark,"stroke")+' d="'+function(e,t,r){let i="",n=0,a=!1,o=0;for(let l=0;l<e.length;l++){let c=Math.floor(l%t),d=Math.floor(l/t);c||a||(a=!0),e[l]?(o++,l>0&&c>0&&e[l-1]||(i+=a?s("M",c+r,.5+d+r):s("m",n,0),n=0,a=!1),c+1<t&&e[l+1]||(i+=s("h",o),o=0)):n++}return i}(l,o,a.margin)+'"/>',u='<svg xmlns="http://www.w3.org/2000/svg" '+(a.width?'width="'+a.width+'" height="'+a.width+'" ':"")+('viewBox="0 0 '+c)+" "+c+'" shape-rendering="crispEdges">'+d+h+"</svg>\n";return"function"==typeof r&&r(null,u),u}},7979:(e,t,r)=>{let i=r(975);t.render=i.render,t.renderToFile=function(e,i,n,s){void 0===s&&(s=n,n=void 0);let a=r(2048),o=t.render(i,n);a.writeFile(e,'<?xml version="1.0" encoding="utf-8"?><!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">'+o,s)}},3678:(e,t,r)=>{let i=r(8821),n=r(3853);t.render=function(e,t,r){return t&&t.small?n.render(e,t,r):i.render(e,t,r)}},3853:(e,t)=>{let r="\x1b[37m",i="\x1b[30m",n="\x1b[0m",s="\x1b[47m"+i,a="\x1b[40m"+r,o=function(e,t,r,i){let n=t+1;return r>=n||i>=n||i<-1||r<-1?"0":r>=t||i>=t||i<0||r<0?"1":e[i*t+r]?"2":"1"},l=function(e,t,r,i){return o(e,t,r,i)+o(e,t,r,i+1)};t.render=function(e,t,o){var c,d;let h=e.modules.size,u=e.modules.data,p=!!(t&&t.inverse),f=t&&t.inverse?a:s,m={"00":n+" "+f,"01":n+(c=p?i:r)+"▄"+f,"02":n+(d=p?r:i)+"▄"+f,10:n+c+"▀"+f,11:" ",12:"▄",20:n+d+"▀"+f,21:"▀",22:"█"},g=n+"\n"+f,x=f;for(let e=-1;e<h+1;e+=2){for(let t=-1;t<h;t++)x+=m[l(u,h,t,e)];x+=m[l(u,h,h,e)]+g}return x+=n,"function"==typeof o&&o(null,x),x}},8821:(e,t)=>{t.render=function(e,t,r){let i=e.modules.size,n=e.modules.data,s="\x1b[47m  \x1b[0m",a="",o=Array(i+3).join(s),l=[,,].join(s);a+=o+"\n";for(let e=0;e<i;++e){a+=s;for(let t=0;t<i;t++)a+=n[e*i+t]?"\x1b[40m  \x1b[0m":s;a+=l+"\n"}return a+=o+"\n","function"==typeof r&&r(null,a),a}},6318:(e,t,r)=>{let i=r(802),n={WW:" ",WB:"▄",BB:"█",BW:"▀"},s={BB:" ",BW:"▄",WW:"█",WB:"▀"};t.render=function(e,t,r){let a=i.getOptions(t),o=n;("#ffffff"===a.color.dark.hex||"#000000"===a.color.light.hex)&&(o=s);let l=e.modules.size,c=e.modules.data,d="",h=Array(l+2*a.margin+1).join(o.WW);h=Array(a.margin/2+1).join(h+"\n");let u=Array(a.margin+1).join(o.WW);d+=h;for(let e=0;e<l;e+=2){d+=u;for(let t=0;t<l;t++){var p;let r=c[e*l+t],i=c[(e+1)*l+t];d+=(p=o,r&&i?p.BB:r&&!i?p.BW:!r&&i?p.WB:p.WW)}d+=u+"\n"}return d+=h.slice(0,-1),"function"==typeof r&&r(null,d),d},t.renderToFile=function(e,i,n,s){void 0===s&&(s=n,n=void 0);let a=r(2048),o=t.render(i,n);a.writeFile(e,o,s)}},802:(e,t)=>{function r(e){if("number"==typeof e&&(e=e.toString()),"string"!=typeof e)throw Error("Color should be defined as hex string");let t=e.slice().replace("#","").split("");if(t.length<3||5===t.length||t.length>8)throw Error("Invalid hex color: "+e);(3===t.length||4===t.length)&&(t=Array.prototype.concat.apply([],t.map(function(e){return[e,e]}))),6===t.length&&t.push("F","F");let r=parseInt(t.join(""),16);return{r:r>>24&255,g:r>>16&255,b:r>>8&255,a:255&r,hex:"#"+t.slice(0,6).join("")}}t.getOptions=function(e){e||(e={}),e.color||(e.color={});let t=void 0===e.margin||null===e.margin||e.margin<0?4:e.margin,i=e.width&&e.width>=21?e.width:void 0,n=e.scale||4;return{width:i,scale:i?4:n,margin:t,color:{dark:r(e.color.dark||"#000000ff"),light:r(e.color.light||"#ffffffff")},type:e.type,rendererOpts:e.rendererOpts||{}}},t.getScale=function(e,t){return t.width&&t.width>=e+2*t.margin?t.width/(e+2*t.margin):t.scale},t.getImageWidth=function(e,r){let i=t.getScale(e,r);return Math.floor((e+2*r.margin)*i)},t.qrToImageData=function(e,r,i){let n=r.modules.size,s=r.modules.data,a=t.getScale(n,i),o=Math.floor((n+2*i.margin)*a),l=i.margin*a,c=[i.color.light,i.color.dark];for(let t=0;t<o;t++)for(let r=0;r<o;r++){let d=(t*o+r)*4,h=i.color.light;t>=l&&r>=l&&t<o-l&&r<o-l&&(h=c[s[Math.floor((t-l)/a)*n+Math.floor((r-l)/a)]?1:0]),e[d++]=h.r,e[d++]=h.g,e[d++]=h.b,e[d]=h.a}}},5570:(e,t,r)=>{let i=r(1817),n=r(8895),s=r(8276),a=r(6318),o=r(3678),l=r(7979);function c(e,t,r){if(void 0===e)throw Error("String required as first argument");if(void 0===r&&(r=t,t={}),"function"!=typeof r){if(i())t=r||{},r=null;else throw Error("Callback required as last argument")}return{opts:t,cb:r}}function d(e){switch(e){case"svg":return l;case"txt":case"utf8":return a;default:return s}}function h(e,t,r){if(!r.cb)return new Promise(function(i,s){try{let a=n.create(t,r.opts);return e(a,r.opts,function(e,t){return e?s(e):i(t)})}catch(e){s(e)}});try{let i=n.create(t,r.opts);return e(i,r.opts,r.cb)}catch(e){r.cb(e)}}t.create=n.create,t.toCanvas=r(9157).toCanvas,t.toString=function(e,t,r){let i=c(e,t,r);return h(function(e){switch(e){case"svg":return l;case"terminal":return o;default:return a}}(i.opts?i.opts.type:void 0).render,e,i)},t.toDataURL=function(e,t,r){let i=c(e,t,r);return h(d(i.opts.type).renderToDataURL,e,i)},t.toBuffer=function(e,t,r){let i=c(e,t,r);return h(d(i.opts.type).renderToBuffer,e,i)},t.toFile=function(e,t,r,n){if("string"!=typeof e||!("string"==typeof t||"object"==typeof t))throw Error("Invalid argument");if(arguments.length<3&&!i())throw Error("Too few arguments provided");let s=c(t,r,n);return h(d(s.opts.type||e.slice((e.lastIndexOf(".")-1>>>0)+2).toLowerCase()).renderToFile.bind(null,e),t,s)},t.toFileStream=function(e,t,r){if(arguments.length<2)throw Error("Too few arguments provided");let i=c(t,r,e.emit.bind(e,"error"));h(d("png").renderToFileStream.bind(null,e),t,i)}},2091:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>e0});var i=r(326),n=r(7577),s=r.n(n),a={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let o=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),l=(e,t)=>{let r=(0,n.forwardRef)(({color:r="currentColor",size:i=24,strokeWidth:s=2,absoluteStrokeWidth:l,className:c="",children:d,...h},u)=>(0,n.createElement)("svg",{ref:u,...a,width:i,height:i,stroke:r,strokeWidth:l?24*Number(s)/Number(i):s,className:["lucide",`lucide-${o(e)}`,c].join(" "),...h},[...t.map(([e,t])=>(0,n.createElement)(e,t)),...Array.isArray(d)?d:[d]]));return r.displayName=`${e}`,r},c=l("Zap",[["polygon",{points:"13 2 3 14 12 14 11 22 21 10 12 10 13 2",key:"45s27k"}]]),d=l("Github",[["path",{d:"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4",key:"tonef"}],["path",{d:"M9 18c-4.51 2-5-2-7-2",key:"9comsn"}]]),h=l("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]]),u=l("QrCode",[["rect",{width:"5",height:"5",x:"3",y:"3",rx:"1",key:"1tu5fj"}],["rect",{width:"5",height:"5",x:"16",y:"3",rx:"1",key:"1v8r4q"}],["rect",{width:"5",height:"5",x:"3",y:"16",rx:"1",key:"1x03jg"}],["path",{d:"M21 16h-3a2 2 0 0 0-2 2v3",key:"177gqh"}],["path",{d:"M21 21v.01",key:"ents32"}],["path",{d:"M12 7v3a2 2 0 0 1-2 2H7",key:"8crl2c"}],["path",{d:"M3 12h.01",key:"nlz23k"}],["path",{d:"M12 3h.01",key:"n36tog"}],["path",{d:"M12 16v.01",key:"133mhm"}],["path",{d:"M16 12h1",key:"1slzba"}],["path",{d:"M21 12v.01",key:"1lwtk9"}],["path",{d:"M12 21v-1",key:"1880an"}]]),p=l("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]),f=l("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]),m=l("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]),g={"User-Agent":"Mozilla/5.0 BiliDroid/1.12.0 (<EMAIL>)","Content-Type":"application/x-www-form-urlencoded",Accept:"application/json","Accept-Language":"zh-CN,zh;q=0.9,en;q=0.8","x-bili-metadata-legal-region":"CN","x-bili-aurora-eid":"","x-bili-aurora-zone":""},x=null;function b(){let e=Math.floor(Date.now()/1e3),t=Math.random().toString(36).substring(2,15);return`${e}_${t}`}async function y(e,t={}){try{let r=new URLSearchParams;Object.entries(t).forEach(([e,t])=>{null!=t&&r.append(e,String(t))});let i={...g};x&&(i["x-bili-mid"]=x.mid,i.cookie=x.cookie),i["x-bili-ticket"]=b();let n=`${e}?${r.toString()}`,s=await fetch(n,{method:"GET",headers:i,mode:"cors"});if(!s.ok)throw Error(`HTTP error! status: ${s.status}`);return await s.json()}catch(e){throw console.error("API GET request failed:",e),e}}async function w(e,t={}){try{let r=new URLSearchParams;Object.entries(t).forEach(([e,t])=>{null!=t&&r.append(e,String(t))});let i={...g};x&&(i["x-bili-mid"]=x.mid,i.cookie=x.cookie),i["x-bili-ticket"]=b();let n=await fetch(e,{method:"POST",headers:i,body:r,mode:"cors"});if(!n.ok)throw Error(`HTTP error! status: ${n.status}`);return await n.json()}catch(e){throw console.error("API POST request failed:",e),e}}let _=e=>{let t=j(e),{conflictingClassGroups:r,conflictingClassGroupModifiers:i}=e;return{getClassGroupId:e=>{let r=e.split("-");return""===r[0]&&1!==r.length&&r.shift(),v(r,t)||N(e)},getConflictingClassGroupIds:(e,t)=>{let n=r[e]||[];return t&&i[e]?[...n,...i[e]]:n}}},v=(e,t)=>{if(0===e.length)return t.classGroupId;let r=e[0],i=t.nextPart.get(r),n=i?v(e.slice(1),i):void 0;if(n)return n;if(0===t.validators.length)return;let s=e.join("-");return t.validators.find(({validator:e})=>e(s))?.classGroupId},k=/^\[(.+)\]$/,N=e=>{if(k.test(e)){let t=k.exec(e)[1],r=t?.substring(0,t.indexOf(":"));if(r)return"arbitrary.."+r}},j=e=>{let{theme:t,prefix:r}=e,i={nextPart:new Map,validators:[]};return T(Object.entries(e.classGroups),r).forEach(([e,r])=>{E(r,i,e,t)}),i},E=(e,t,r,i)=>{e.forEach(e=>{if("string"==typeof e){(""===e?t:A(t,e)).classGroupId=r;return}if("function"==typeof e){if(C(e)){E(e(i),t,r,i);return}t.validators.push({validator:e,classGroupId:r});return}Object.entries(e).forEach(([e,n])=>{E(n,A(t,e),r,i)})})},A=(e,t)=>{let r=e;return t.split("-").forEach(e=>{r.nextPart.has(e)||r.nextPart.set(e,{nextPart:new Map,validators:[]}),r=r.nextPart.get(e)}),r},C=e=>e.isThemeGetter,T=(e,t)=>t?e.map(([e,r])=>[e,r.map(e=>"string"==typeof e?t+e:"object"==typeof e?Object.fromEntries(Object.entries(e).map(([e,r])=>[t+e,r])):e)]):e,I=e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let t=0,r=new Map,i=new Map,n=(n,s)=>{r.set(n,s),++t>e&&(t=0,i=r,r=new Map)};return{get(e){let t=r.get(e);return void 0!==t?t:void 0!==(t=i.get(e))?(n(e,t),t):void 0},set(e,t){r.has(e)?r.set(e,t):n(e,t)}}},P=e=>{let{separator:t,experimentalParseClassName:r}=e,i=1===t.length,n=t[0],s=t.length,a=e=>{let r;let a=[],o=0,l=0;for(let c=0;c<e.length;c++){let d=e[c];if(0===o){if(d===n&&(i||e.slice(c,c+s)===t)){a.push(e.slice(l,c)),l=c+s;continue}if("/"===d){r=c;continue}}"["===d?o++:"]"===d&&o--}let c=0===a.length?e:e.substring(l),d=c.startsWith("!"),h=d?c.substring(1):c;return{modifiers:a,hasImportantModifier:d,baseClassName:h,maybePostfixModifierPosition:r&&r>l?r-l:void 0}};return r?e=>r({className:e,parseClassName:a}):a},M=e=>{if(e.length<=1)return e;let t=[],r=[];return e.forEach(e=>{"["===e[0]?(t.push(...r.sort(),e),r=[]):r.push(e)}),t.push(...r.sort()),t},L=e=>({cache:I(e.cacheSize),parseClassName:P(e),..._(e)}),S=/\s+/,O=(e,t)=>{let{parseClassName:r,getClassGroupId:i,getConflictingClassGroupIds:n}=t,s=[],a=e.trim().split(S),o="";for(let e=a.length-1;e>=0;e-=1){let t=a[e],{modifiers:l,hasImportantModifier:c,baseClassName:d,maybePostfixModifierPosition:h}=r(t),u=!!h,p=i(u?d.substring(0,h):d);if(!p){if(!u||!(p=i(d))){o=t+(o.length>0?" "+o:o);continue}u=!1}let f=M(l).join(":"),m=c?f+"!":f,g=m+p;if(s.includes(g))continue;s.push(g);let x=n(p,u);for(let e=0;e<x.length;++e){let t=x[e];s.push(m+t)}o=t+(o.length>0?" "+o:o)}return o};function R(){let e,t,r=0,i="";for(;r<arguments.length;)(e=arguments[r++])&&(t=B(e))&&(i&&(i+=" "),i+=t);return i}let B=e=>{let t;if("string"==typeof e)return e;let r="";for(let i=0;i<e.length;i++)e[i]&&(t=B(e[i]))&&(r&&(r+=" "),r+=t);return r},D=e=>{let t=t=>t[e]||[];return t.isThemeGetter=!0,t},z=/^\[(?:([a-z-]+):)?(.+)\]$/i,U=/^\d+\/\d+$/,H=new Set(["px","full","screen"]),Y=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,F=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,q=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,G=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,$=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,V=e=>K(e)||H.has(e)||U.test(e),W=e=>el(e,"length",ec),K=e=>!!e&&!Number.isNaN(Number(e)),J=e=>el(e,"number",K),Z=e=>!!e&&Number.isInteger(Number(e)),Q=e=>e.endsWith("%")&&K(e.slice(0,-1)),X=e=>z.test(e),ee=e=>Y.test(e),et=new Set(["length","size","percentage"]),er=e=>el(e,et,ed),ei=e=>el(e,"position",ed),en=new Set(["image","url"]),es=e=>el(e,en,eu),ea=e=>el(e,"",eh),eo=()=>!0,el=(e,t,r)=>{let i=z.exec(e);return!!i&&(i[1]?"string"==typeof t?i[1]===t:t.has(i[1]):r(i[2]))},ec=e=>F.test(e)&&!q.test(e),ed=()=>!1,eh=e=>G.test(e),eu=e=>$.test(e);Symbol.toStringTag;let ep=function(e,...t){let r,i,n;let s=function(o){return i=(r=L(t.reduce((e,t)=>t(e),e()))).cache.get,n=r.cache.set,s=a,a(o)};function a(e){let t=i(e);if(t)return t;let s=O(e,r);return n(e,s),s}return function(){return s(R.apply(null,arguments))}}(()=>{let e=D("colors"),t=D("spacing"),r=D("blur"),i=D("brightness"),n=D("borderColor"),s=D("borderRadius"),a=D("borderSpacing"),o=D("borderWidth"),l=D("contrast"),c=D("grayscale"),d=D("hueRotate"),h=D("invert"),u=D("gap"),p=D("gradientColorStops"),f=D("gradientColorStopPositions"),m=D("inset"),g=D("margin"),x=D("opacity"),b=D("padding"),y=D("saturate"),w=D("scale"),_=D("sepia"),v=D("skew"),k=D("space"),N=D("translate"),j=()=>["auto","contain","none"],E=()=>["auto","hidden","clip","visible","scroll"],A=()=>["auto",X,t],C=()=>[X,t],T=()=>["",V,W],I=()=>["auto",K,X],P=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],M=()=>["solid","dashed","dotted","double","none"],L=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],S=()=>["start","end","center","between","around","evenly","stretch"],O=()=>["","0",X],R=()=>["auto","avoid","all","avoid-page","page","left","right","column"],B=()=>[K,X];return{cacheSize:500,separator:":",theme:{colors:[eo],spacing:[V,W],blur:["none","",ee,X],brightness:B(),borderColor:[e],borderRadius:["none","","full",ee,X],borderSpacing:C(),borderWidth:T(),contrast:B(),grayscale:O(),hueRotate:B(),invert:O(),gap:C(),gradientColorStops:[e],gradientColorStopPositions:[Q,W],inset:A(),margin:A(),opacity:B(),padding:C(),saturate:B(),scale:B(),sepia:O(),skew:B(),space:C(),translate:C()},classGroups:{aspect:[{aspect:["auto","square","video",X]}],container:["container"],columns:[{columns:[ee]}],"break-after":[{"break-after":R()}],"break-before":[{"break-before":R()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...P(),X]}],overflow:[{overflow:E()}],"overflow-x":[{"overflow-x":E()}],"overflow-y":[{"overflow-y":E()}],overscroll:[{overscroll:j()}],"overscroll-x":[{"overscroll-x":j()}],"overscroll-y":[{"overscroll-y":j()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[m]}],"inset-x":[{"inset-x":[m]}],"inset-y":[{"inset-y":[m]}],start:[{start:[m]}],end:[{end:[m]}],top:[{top:[m]}],right:[{right:[m]}],bottom:[{bottom:[m]}],left:[{left:[m]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",Z,X]}],basis:[{basis:A()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",X]}],grow:[{grow:O()}],shrink:[{shrink:O()}],order:[{order:["first","last","none",Z,X]}],"grid-cols":[{"grid-cols":[eo]}],"col-start-end":[{col:["auto",{span:["full",Z,X]},X]}],"col-start":[{"col-start":I()}],"col-end":[{"col-end":I()}],"grid-rows":[{"grid-rows":[eo]}],"row-start-end":[{row:["auto",{span:[Z,X]},X]}],"row-start":[{"row-start":I()}],"row-end":[{"row-end":I()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",X]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",X]}],gap:[{gap:[u]}],"gap-x":[{"gap-x":[u]}],"gap-y":[{"gap-y":[u]}],"justify-content":[{justify:["normal",...S()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...S(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...S(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[b]}],px:[{px:[b]}],py:[{py:[b]}],ps:[{ps:[b]}],pe:[{pe:[b]}],pt:[{pt:[b]}],pr:[{pr:[b]}],pb:[{pb:[b]}],pl:[{pl:[b]}],m:[{m:[g]}],mx:[{mx:[g]}],my:[{my:[g]}],ms:[{ms:[g]}],me:[{me:[g]}],mt:[{mt:[g]}],mr:[{mr:[g]}],mb:[{mb:[g]}],ml:[{ml:[g]}],"space-x":[{"space-x":[k]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[k]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",X,t]}],"min-w":[{"min-w":[X,t,"min","max","fit"]}],"max-w":[{"max-w":[X,t,"none","full","min","max","fit","prose",{screen:[ee]},ee]}],h:[{h:[X,t,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[X,t,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[X,t,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[X,t,"auto","min","max","fit"]}],"font-size":[{text:["base",ee,W]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",J]}],"font-family":[{font:[eo]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",X]}],"line-clamp":[{"line-clamp":["none",K,J]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",V,X]}],"list-image":[{"list-image":["none",X]}],"list-style-type":[{list:["none","disc","decimal",X]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[x]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[x]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...M(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",V,W]}],"underline-offset":[{"underline-offset":["auto",V,X]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:C()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",X]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",X]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[x]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...P(),ei]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",er]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},es]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[f]}],"gradient-via-pos":[{via:[f]}],"gradient-to-pos":[{to:[f]}],"gradient-from":[{from:[p]}],"gradient-via":[{via:[p]}],"gradient-to":[{to:[p]}],rounded:[{rounded:[s]}],"rounded-s":[{"rounded-s":[s]}],"rounded-e":[{"rounded-e":[s]}],"rounded-t":[{"rounded-t":[s]}],"rounded-r":[{"rounded-r":[s]}],"rounded-b":[{"rounded-b":[s]}],"rounded-l":[{"rounded-l":[s]}],"rounded-ss":[{"rounded-ss":[s]}],"rounded-se":[{"rounded-se":[s]}],"rounded-ee":[{"rounded-ee":[s]}],"rounded-es":[{"rounded-es":[s]}],"rounded-tl":[{"rounded-tl":[s]}],"rounded-tr":[{"rounded-tr":[s]}],"rounded-br":[{"rounded-br":[s]}],"rounded-bl":[{"rounded-bl":[s]}],"border-w":[{border:[o]}],"border-w-x":[{"border-x":[o]}],"border-w-y":[{"border-y":[o]}],"border-w-s":[{"border-s":[o]}],"border-w-e":[{"border-e":[o]}],"border-w-t":[{"border-t":[o]}],"border-w-r":[{"border-r":[o]}],"border-w-b":[{"border-b":[o]}],"border-w-l":[{"border-l":[o]}],"border-opacity":[{"border-opacity":[x]}],"border-style":[{border:[...M(),"hidden"]}],"divide-x":[{"divide-x":[o]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[o]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[x]}],"divide-style":[{divide:M()}],"border-color":[{border:[n]}],"border-color-x":[{"border-x":[n]}],"border-color-y":[{"border-y":[n]}],"border-color-s":[{"border-s":[n]}],"border-color-e":[{"border-e":[n]}],"border-color-t":[{"border-t":[n]}],"border-color-r":[{"border-r":[n]}],"border-color-b":[{"border-b":[n]}],"border-color-l":[{"border-l":[n]}],"divide-color":[{divide:[n]}],"outline-style":[{outline:["",...M()]}],"outline-offset":[{"outline-offset":[V,X]}],"outline-w":[{outline:[V,W]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:T()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[x]}],"ring-offset-w":[{"ring-offset":[V,W]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",ee,ea]}],"shadow-color":[{shadow:[eo]}],opacity:[{opacity:[x]}],"mix-blend":[{"mix-blend":[...L(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":L()}],filter:[{filter:["","none"]}],blur:[{blur:[r]}],brightness:[{brightness:[i]}],contrast:[{contrast:[l]}],"drop-shadow":[{"drop-shadow":["","none",ee,X]}],grayscale:[{grayscale:[c]}],"hue-rotate":[{"hue-rotate":[d]}],invert:[{invert:[h]}],saturate:[{saturate:[y]}],sepia:[{sepia:[_]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[r]}],"backdrop-brightness":[{"backdrop-brightness":[i]}],"backdrop-contrast":[{"backdrop-contrast":[l]}],"backdrop-grayscale":[{"backdrop-grayscale":[c]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[d]}],"backdrop-invert":[{"backdrop-invert":[h]}],"backdrop-opacity":[{"backdrop-opacity":[x]}],"backdrop-saturate":[{"backdrop-saturate":[y]}],"backdrop-sepia":[{"backdrop-sepia":[_]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[a]}],"border-spacing-x":[{"border-spacing-x":[a]}],"border-spacing-y":[{"border-spacing-y":[a]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",X]}],duration:[{duration:B()}],ease:[{ease:["linear","in","out","in-out",X]}],delay:[{delay:B()}],animate:[{animate:["none","spin","ping","pulse","bounce",X]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[w]}],"scale-x":[{"scale-x":[w]}],"scale-y":[{"scale-y":[w]}],rotate:[{rotate:[Z,X]}],"translate-x":[{"translate-x":[N]}],"translate-y":[{"translate-y":[N]}],"skew-x":[{"skew-x":[v]}],"skew-y":[{"skew-y":[v]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",X]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",X]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":C()}],"scroll-mx":[{"scroll-mx":C()}],"scroll-my":[{"scroll-my":C()}],"scroll-ms":[{"scroll-ms":C()}],"scroll-me":[{"scroll-me":C()}],"scroll-mt":[{"scroll-mt":C()}],"scroll-mr":[{"scroll-mr":C()}],"scroll-mb":[{"scroll-mb":C()}],"scroll-ml":[{"scroll-ml":C()}],"scroll-p":[{"scroll-p":C()}],"scroll-px":[{"scroll-px":C()}],"scroll-py":[{"scroll-py":C()}],"scroll-ps":[{"scroll-ps":C()}],"scroll-pe":[{"scroll-pe":C()}],"scroll-pt":[{"scroll-pt":C()}],"scroll-pr":[{"scroll-pr":C()}],"scroll-pb":[{"scroll-pb":C()}],"scroll-pl":[{"scroll-pl":C()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",X]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[V,W,J]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}});function ef(...e){return ep(function(){for(var e,t,r=0,i="",n=arguments.length;r<n;r++)(e=arguments[r])&&(t=function e(t){var r,i,n="";if("string"==typeof t||"number"==typeof t)n+=t;else if("object"==typeof t){if(Array.isArray(t)){var s=t.length;for(r=0;r<s;r++)t[r]&&(i=e(t[r]))&&(n&&(n+=" "),n+=i)}else for(i in t)t[i]&&(n&&(n+=" "),n+=i)}return n}(e))&&(i&&(i+=" "),i+=t);return i}(e))}let em={set:(e,t)=>{},remove:e=>{},clear:()=>{}};function eg(e,t){if(!e||"string"!=typeof e)return!1;switch(t){case"deepseek":return e.startsWith("sk-")&&e.length>10;case"gemini":return e.length>20;case"openai":return e.startsWith("sk-")&&e.length>20;default:return!1}}let ex={AUTH_DATA:"bili-hardcore-auth",AI_CONFIG:"bili-hardcore-ai-config"};async function eb(){try{let e=await fetch("https://passport.bilibili.com/x/passport-login/web/qrcode/generate",{method:"GET",headers:{"User-Agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",Referer:"https://www.bilibili.com/"}});if(!e.ok)throw Error(`HTTP error! status: ${e.status}`);let t=await e.json();if(0!==t.code)throw Error(t.message||"获取二维码失败");return{url:t.data.url,auth_code:t.data.qrcode_key}}catch(e){throw console.error("获取二维码失败:",e),e}}async function ey(e){try{let t=await fetch(`https://passport.bilibili.com/x/passport-login/web/qrcode/poll?qrcode_key=${e}`,{method:"GET",headers:{"User-Agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",Referer:"https://www.bilibili.com/"}});if(!t.ok)throw Error(`HTTP error! status: ${t.status}`);return await t.json()}catch(e){throw console.error("轮询二维码状态失败:",e),e}}var ew=r(1270);async function e_(e){try{return await ew.toDataURL(e,{errorCorrectionLevel:"M",type:"image/png",quality:.92,margin:1,color:{dark:"#000000",light:"#FFFFFF"},width:256})}catch(e){throw console.error("生成二维码失败:",e),Error("生成二维码失败")}}function ev({onLoginSuccess:e,className:t}){let[r,s]=(0,n.useState)("idle"),[a,o]=(0,n.useState)(null),[l,c]=(0,n.useState)(""),[d,h]=(0,n.useState)(""),[g,b]=(0,n.useState)(0),y=async()=>{try{s("loading"),h("");let e=await eb();o(e);let t=await e_(e.url);c(t),s("waiting"),w(e.auth_code)}catch(e){h("获取二维码失败，请重试"),s("error")}},w=t=>{let r=0,i=setInterval(async()=>{try{if(r>=60){clearInterval(i),s("expired");return}let n=await ey(t);if(0===n.code&&n.data){clearInterval(i),s("success");let t=n.data.cookie_info.cookies,r="";for(let e of t)if("bili_jct"===e.name){r=e.value;break}let a=t.map(e=>`${e.name}=${e.value}`).join(";"),o={access_token:n.data.access_token,csrf:r,mid:String(n.data.mid),cookie:a};(function(e){try{let t={...e,timestamp:Date.now()};em.set(ex.AUTH_DATA,t),x=e}catch(e){throw console.error("保存认证信息失败:",e),e}})(o),e(o)}else 86101===n.code?r++:86090===n.code?(s("scanned"),r++):86038===n.code?(clearInterval(i),s("expired")):(clearInterval(i),h(n.message||"登录失败"),s("error"))}catch(e){++r>=60&&(clearInterval(i),h("网络连接超时，请重试"),s("error"))}},1e3)},_=()=>{b(e=>e+1),y()};return i.jsx("div",{className:ef("card max-w-md mx-auto",t),children:(0,i.jsxs)("div",{className:"text-center",children:[i.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-6",children:"哔哩哔哩登录"}),i.jsx("div",{className:"mb-6",children:(0,i.jsxs)("div",{className:"relative inline-block",children:[l&&"loading"!==r?i.jsx("img",{src:l,alt:"登录二维码",className:ef("w-64 h-64 border-2 border-gray-200 rounded-lg","expired"===r&&"opacity-50 grayscale")}):i.jsx("div",{className:"w-64 h-64 border-2 border-gray-200 rounded-lg flex items-center justify-center bg-gray-50",children:i.jsx("div",{className:"w-8 h-8 loading-spinner"})}),("expired"===r||"error"===r)&&i.jsx("button",{onClick:_,className:"absolute inset-0 flex items-center justify-center bg-black bg-opacity-50 rounded-lg text-white hover:bg-opacity-60 transition-colors",children:i.jsx(m,{className:"w-8 h-8"})})]})}),(0,i.jsxs)("div",{className:"flex items-center justify-center gap-2 mb-4",children:[(()=>{switch(r){case"loading":return i.jsx("div",{className:"w-5 h-5 loading-spinner"});case"waiting":return i.jsx(u,{className:"w-5 h-5 text-bili-blue"});case"scanned":return i.jsx(p,{className:"w-5 h-5 text-yellow-500"});case"success":return i.jsx(p,{className:"w-5 h-5 text-green-500"});case"expired":case"error":return i.jsx(f,{className:"w-5 h-5 text-red-500"});default:return null}})(),i.jsx("span",{className:ef("text-sm","error"===r||"expired"===r?"text-red-600":"success"===r?"text-green-600":"scanned"===r?"text-yellow-600":"text-gray-600"),children:(()=>{switch(r){case"loading":return"正在生成二维码...";case"waiting":return"请使用哔哩哔哩APP扫描二维码登录";case"scanned":return"扫描成功，请在手机上确认登录";case"success":return"登录成功！";case"expired":return"二维码已过期，请点击刷新";case"error":return d||"发生错误，请重试";default:return""}})()})]}),(0,i.jsxs)("div",{className:"text-xs text-gray-500 space-y-1",children:[i.jsx("p",{children:"1. 打开哔哩哔哩APP"}),i.jsx("p",{children:'2. 点击右下角"我的"'}),i.jsx("p",{children:"3. 点击右上角扫码图标"}),i.jsx("p",{children:"4. 扫描上方二维码完成登录"})]}),"loading"!==r&&"success"!==r&&(0,i.jsxs)("button",{onClick:_,className:"mt-4 btn-outline px-4 py-2 text-sm",children:[i.jsx(m,{className:"w-4 h-4 mr-2"}),"刷新二维码"]})]})})}let ek=l("Bot",[["path",{d:"M12 8V4H8",key:"hb8ula"}],["rect",{width:"16",height:"12",x:"4",y:"8",rx:"2",key:"enze0r"}],["path",{d:"M2 14h2",key:"vft8re"}],["path",{d:"M20 14h2",key:"4cs60a"}],["path",{d:"M15 13v2",key:"1xurst"}],["path",{d:"M9 13v2",key:"rq6x2g"}]]),eN=l("EyeOff",[["path",{d:"M9.88 9.88a3 3 0 1 0 4.24 4.24",key:"1jxqfv"}],["path",{d:"M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68",key:"9wicm4"}],["path",{d:"M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61",key:"1jreej"}],["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}]]),ej=l("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),eE=l("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),eA=`
当前时间：{timestamp}
你是一个高效精准的答题专家，面对选择题时，直接根据问题和选项判断正确答案，并返回对应选项的序号（1, 2, 3, 4）。示例：
问题：大的反义词是什么？
选项：['长', '宽', '小', '热']
回答：3
如果不确定正确答案，选择最接近的选项序号返回，不提供额外解释或超出 1-4 的内容。
---
请回答我的问题：{question}
`;class eC{constructor(e){this.baseUrl="https://api.deepseek.com/v1",this.model="deepseek-chat",this.apiKey=e}async ask(e,t=3e4){let r=`${this.baseUrl}/chat/completions`,i={"Content-Type":"application/json",Authorization:`Bearer ${this.apiKey}`},n={model:this.model,messages:[{role:"user",content:eA.replace("{timestamp}",Date.now().toString()).replace("{question}",e)}]};try{let e=new AbortController,s=setTimeout(()=>e.abort(),t),a=await fetch(r,{method:"POST",headers:i,body:JSON.stringify(n),signal:e.signal});if(clearTimeout(s),!a.ok)throw Error(`DeepSeek API error: ${a.status} ${a.statusText}`);return(await a.json()).choices[0].message.content.trim()}catch(e){throw console.error("DeepSeek API request failed:",e),Error(`DeepSeek API请求失败: ${e instanceof Error?e.message:"未知错误"}`)}}}class eT{constructor(e){this.baseUrl="https://generativelanguage.googleapis.com/v1beta",this.model="gemini-2.0-flash-exp",this.apiKey=e}async ask(e,t=3e4){let r=`${this.baseUrl}/models/${this.model}:generateContent?key=${this.apiKey}`,i={contents:[{parts:[{text:eA.replace("{timestamp}",Date.now().toString()).replace("{question}",e)}]}]};try{let e=new AbortController,n=setTimeout(()=>e.abort(),t),s=await fetch(r,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(i),signal:e.signal});if(clearTimeout(n),!s.ok)throw Error(`Gemini API error: ${s.status} ${s.statusText}`);return(await s.json()).candidates[0].content.parts[0].text.trim()}catch(e){throw console.error("Gemini API request failed:",e),Error(`Gemini API请求失败: ${e instanceof Error?e.message:"未知错误"}`)}}}class eI{constructor(e,t,r){this.baseUrl=e.endsWith("/")?e.slice(0,-1):e,this.model=t,this.apiKey=r}async ask(e,t=3e4){let r=`${this.baseUrl}/chat/completions`,i={"Content-Type":"application/json",Authorization:`Bearer ${this.apiKey}`},n={model:this.model,messages:[{role:"user",content:eA.replace("{timestamp}",Date.now().toString()).replace("{question}",e)}]};try{let e=new AbortController,s=setTimeout(()=>e.abort(),t),a=await fetch(r,{method:"POST",headers:i,body:JSON.stringify(n),signal:e.signal});if(clearTimeout(s),!a.ok)throw Error(`OpenAI API error: ${a.status} ${a.statusText}`);return(await a.json()).choices[0].message.content.trim()}catch(e){throw console.error("OpenAI API request failed:",e),Error(`OpenAI API请求失败: ${e instanceof Error?e.message:"未知错误"}`)}}}function eP({onConfigComplete:e,className:t}){let[r,s]=(0,n.useState)({model_choice:"1"}),[a,o]=(0,n.useState)({deepseek:!1,gemini:!1,openai:!1}),[l,c]=(0,n.useState)({}),[d,h]=(0,n.useState)(!1),u=e=>{s(t=>({...t,model_choice:e})),c({})},p=e=>{s(t=>({...t,api_key_deepseek:e})),l.deepseek&&c(e=>({...e,deepseek:""}))},m=e=>{s(t=>({...t,api_key_gemini:e})),l.gemini&&c(e=>({...e,gemini:""}))},g=(e,t)=>{s(r=>({...r,openai_config:{...r.openai_config,[e]:t}})),l.openai&&c(e=>({...e,openai:""}))},x=async()=>{h(!0),c({});try{let t=function(e){switch(e.model_choice){case"1":if(!e.api_key_deepseek)return{isValid:!1,error:"DeepSeek API密钥不能为空"};if(!eg(e.api_key_deepseek,"deepseek"))return{isValid:!1,error:"DeepSeek API密钥格式不正确"};break;case"2":if(!e.api_key_gemini)return{isValid:!1,error:"Gemini API密钥不能为空"};if(!eg(e.api_key_gemini,"gemini"))return{isValid:!1,error:"Gemini API密钥格式不正确"};break;case"3":if(!e.openai_config)return{isValid:!1,error:"OpenAI配置不能为空"};let{base_url:t,model:r,api_key:i}=e.openai_config;if(!t||!r||!i)return{isValid:!1,error:"OpenAI配置不完整"};if(!eg(i,"openai"))return{isValid:!1,error:"OpenAI API密钥格式不正确"};break;default:return{isValid:!1,error:"无效的AI模型选择"}}return{isValid:!0}}(r);if(!t.isValid){c({general:t.error||"配置验证失败"});return}(function(e){try{em.set(ex.AI_CONFIG,e)}catch(e){throw console.error("保存AI配置失败:",e),e}})(r),e(r)}catch(e){c({general:"保存配置失败，请重试"})}finally{h(!1)}},b=e=>{o(t=>({...t,[e]:!t[e]}))};return(0,i.jsxs)("div",{className:ef("card max-w-2xl mx-auto",t),children:[(0,i.jsxs)("div",{className:"text-center mb-6",children:[(0,i.jsxs)("div",{className:"flex items-center justify-center gap-2 mb-2",children:[i.jsx(ek,{className:"w-6 h-6 text-bili-blue"}),i.jsx("h2",{className:"text-2xl font-bold text-gray-900",children:"AI模型配置"})]}),i.jsx("p",{className:"text-gray-600",children:"选择并配置用于自动答题的AI模型"})]}),(0,i.jsxs)("div",{className:"mb-6",children:[i.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:"选择AI模型"}),(0,i.jsxs)("div",{className:"space-y-3",children:[(0,i.jsxs)("label",{className:"flex items-center p-3 border rounded-lg cursor-pointer hover:bg-gray-50",children:[i.jsx("input",{type:"radio",name:"model",value:"1",checked:"1"===r.model_choice,onChange:e=>u(e.target.value),className:"mr-3"}),(0,i.jsxs)("div",{children:[i.jsx("div",{className:"font-medium",children:"DeepSeek (V3)"}),i.jsx("div",{className:"text-sm text-gray-500",children:"推荐选择，性价比高"})]})]}),(0,i.jsxs)("label",{className:"flex items-center p-3 border rounded-lg cursor-pointer hover:bg-gray-50",children:[i.jsx("input",{type:"radio",name:"model",value:"2",checked:"2"===r.model_choice,onChange:e=>u(e.target.value),className:"mr-3"}),(0,i.jsxs)("div",{children:[i.jsx("div",{className:"font-medium",children:"Gemini (2.0-flash)"}),i.jsx("div",{className:"text-sm text-gray-500",children:"免费版可能会触发风控429报错"})]})]}),(0,i.jsxs)("label",{className:"flex items-center p-3 border rounded-lg cursor-pointer hover:bg-gray-50",children:[i.jsx("input",{type:"radio",name:"model",value:"3",checked:"3"===r.model_choice,onChange:e=>u(e.target.value),className:"mr-3"}),(0,i.jsxs)("div",{children:[i.jsx("div",{className:"font-medium",children:"自定义OpenAI格式API"}),i.jsx("div",{className:"text-sm text-gray-500",children:"支持OpenAI、火山引擎、硅基流动等"})]})]})]})]}),"1"===r.model_choice&&(0,i.jsxs)("div",{className:"mb-6",children:[i.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"DeepSeek API密钥"}),(0,i.jsxs)("div",{className:"relative",children:[i.jsx("input",{type:a.deepseek?"text":"password",value:r.api_key_deepseek||"",onChange:e=>p(e.target.value),placeholder:"请输入DeepSeek API密钥",className:ef("input pr-10",l.deepseek&&"border-red-500")}),i.jsx("button",{type:"button",onClick:()=>b("deepseek"),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",children:a.deepseek?i.jsx(eN,{className:"w-4 h-4"}):i.jsx(ej,{className:"w-4 h-4"})})]}),l.deepseek&&i.jsx("p",{className:"mt-1 text-sm text-red-600",children:l.deepseek}),(0,i.jsxs)("p",{className:"mt-1 text-xs text-gray-500",children:["获取地址：",i.jsx("a",{href:"https://platform.deepseek.com/api_keys",target:"_blank",rel:"noopener noreferrer",className:"text-bili-blue hover:underline",children:"https://platform.deepseek.com/api_keys"})]})]}),"2"===r.model_choice&&(0,i.jsxs)("div",{className:"mb-6",children:[i.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Gemini API密钥"}),(0,i.jsxs)("div",{className:"relative",children:[i.jsx("input",{type:a.gemini?"text":"password",value:r.api_key_gemini||"",onChange:e=>m(e.target.value),placeholder:"请输入Gemini API密钥",className:ef("input pr-10",l.gemini&&"border-red-500")}),i.jsx("button",{type:"button",onClick:()=>b("gemini"),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",children:a.gemini?i.jsx(eN,{className:"w-4 h-4"}):i.jsx(ej,{className:"w-4 h-4"})})]}),l.gemini&&i.jsx("p",{className:"mt-1 text-sm text-red-600",children:l.gemini}),(0,i.jsxs)("p",{className:"mt-1 text-xs text-gray-500",children:["获取地址：",i.jsx("a",{href:"https://aistudio.google.com/app/apikey",target:"_blank",rel:"noopener noreferrer",className:"text-bili-blue hover:underline",children:"https://aistudio.google.com/app/apikey"})]})]}),"3"===r.model_choice&&(0,i.jsxs)("div",{className:"mb-6 space-y-4",children:[(0,i.jsxs)("div",{children:[i.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"API基础URL"}),i.jsx("input",{type:"text",value:r.openai_config?.base_url||"",onChange:e=>g("base_url",e.target.value),placeholder:"例如：https://ark.cn-beijing.volces.com/api/v3",className:ef("input",l.openai&&"border-red-500")})]}),(0,i.jsxs)("div",{children:[i.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"模型名称"}),i.jsx("input",{type:"text",value:r.openai_config?.model||"",onChange:e=>g("model",e.target.value),placeholder:"例如：deepseek-v3-250324",className:ef("input",l.openai&&"border-red-500")}),i.jsx("p",{className:"mt-1 text-xs text-gray-500",children:"不建议使用思考模型，可能产生意想不到的问题"})]}),(0,i.jsxs)("div",{children:[i.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"API密钥"}),(0,i.jsxs)("div",{className:"relative",children:[i.jsx("input",{type:a.openai?"text":"password",value:r.openai_config?.api_key||"",onChange:e=>g("api_key",e.target.value),placeholder:"请输入API密钥",className:ef("input pr-10",l.openai&&"border-red-500")}),i.jsx("button",{type:"button",onClick:()=>b("openai"),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",children:a.openai?i.jsx(eN,{className:"w-4 h-4"}):i.jsx(ej,{className:"w-4 h-4"})})]})]}),l.openai&&i.jsx("p",{className:"text-sm text-red-600",children:l.openai})]}),l.general&&(0,i.jsxs)("div",{className:"mb-4 p-3 bg-red-50 border border-red-200 rounded-lg flex items-center gap-2",children:[i.jsx(f,{className:"w-4 h-4 text-red-500"}),i.jsx("span",{className:"text-sm text-red-600",children:l.general})]}),i.jsx("button",{onClick:x,disabled:d,className:"w-full btn-primary py-3 disabled:opacity-50 disabled:cursor-not-allowed",children:d?(0,i.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[i.jsx("div",{className:"w-4 h-4 loading-spinner border-white"}),i.jsx("span",{children:"保存中..."})]}):(0,i.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[i.jsx(eE,{className:"w-4 h-4"}),i.jsx("span",{children:"保存配置"})]})})]})}let eM=l("Shield",[["path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10",key:"1irkt0"}]]),eL=l("ExternalLink",[["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}],["polyline",{points:"15 3 21 3 21 9",key:"mznyad"}],["line",{x1:"10",x2:"21",y1:"14",y2:"3",key:"18c3s4"}]]);async function eS(){try{let e=await y("https://api.bilibili.com/x/senior/v1/category",{disable_rcmd:0,mobi_app:"android",platform:"android",statistics:'{"appId":1,"platform":3,"version":"8.40.0","abtest":""}',web_location:"333.790"});if(0===e.code&&e.data)return e.data.categories||[];if(41099===e.code)throw Error("获取分类失败，可能是已经达到答题限制(B站每日限制3次)，请前往B站APP确认是否可以正常答题");throw Error(e.message||"获取分类失败，请前往B站APP确认是否可以正常答题")}catch(e){throw console.error("获取分类失败:",e),e}}async function eO(){try{let e=await y("https://api.bilibili.com/x/senior/v1/captcha",{disable_rcmd:0,mobi_app:"android",platform:"android",statistics:'{"appId":1,"platform":3,"version":"8.40.0","abtest":""}',web_location:"333.790"});if(0===e.code&&e.data)return e.data;throw Error(e.message||"获取验证码失败，请前往B站APP确认是否可以正常答题")}catch(e){throw console.error("获取验证码失败:",e),e}}async function eR(e,t,r){try{let i=await w("https://api.bilibili.com/x/senior/v1/captcha/submit",{bili_code:e,bili_token:t,disable_rcmd:"0",gt_challenge:"",gt_seccode:"",gt_validate:"",ids:r,mobi_app:"android",platform:"android",statistics:'{"appId":1,"platform":3,"version":"8.40.0","abtest":""}',type:"bilibili"});return 0===i.code}catch(e){throw console.error("提交验证码失败:",e),e}}async function eB(){try{return await y("https://api.bilibili.com/x/senior/v1/question",{disable_rcmd:"0",mobi_app:"android",platform:"android",statistics:'{"appId":1,"platform":3,"version":"8.40.0","abtest":""}',web_location:"333.790"})}catch(e){throw console.error("获取题目失败:",e),e}}async function eD(e,t,r){try{return await w("https://api.bilibili.com/x/senior/v1/answer/submit",{id:e,ans_hash:t,ans_text:r,disable_rcmd:"0",mobi_app:"android",platform:"android",statistics:'{"appId":1,"platform":3,"version":"8.40.0","abtest":""}',web_location:"333.790"})}catch(e){throw console.error("提交答案失败:",e),e}}async function ez(){try{let e=await y("https://api.bilibili.com/x/senior/v1/answer/result",{disable_rcmd:"0",mobi_app:"android",platform:"android",statistics:'{"appId":1,"platform":3,"version":"8.40.0","abtest":""}',web_location:"333.790"});if(0===e.code&&e.data)return e.data;throw Error(e.message||"获取答题结果失败")}catch(e){throw console.error("获取答题结果失败:",e),e}}function eU({onCaptchaComplete:e,className:t}){let[r,s]=(0,n.useState)("loading"),[a,o]=(0,n.useState)([]),[l,c]=(0,n.useState)([]),[d,h]=(0,n.useState)(null),[u,g]=(0,n.useState)(""),[x,b]=(0,n.useState)(""),y=async()=>{try{s("loading"),b("");let e=await eS();o(e),s("selecting")}catch(e){b(e instanceof Error?e.message:"获取分类失败"),s("error")}},w=async()=>{try{s("loading"),b("");let e=await eO();h(e),s("captcha")}catch(e){b(e instanceof Error?e.message:"获取验证码失败"),s("error")}},_=e=>{c(t=>t.includes(e)?t.filter(t=>t!==e):t.length>=3?t:[...t,e])},v=async()=>{if(!u.trim()){b("请输入验证码");return}if(!d){b("验证码数据丢失，请重新获取");return}try{s("submitting"),b("");let t=l.join(",");if(await eR(u,d.token,t))e(t);else throw Error("验证码验证失败")}catch(e){b(e instanceof Error?e.message:"验证码提交失败"),s("captcha")}};return(0,i.jsxs)("div",{className:ef("card max-w-2xl mx-auto",t),children:[(0,i.jsxs)("div",{className:"text-center mb-6",children:[(0,i.jsxs)("div",{className:"flex items-center justify-center gap-2 mb-2",children:[(()=>{switch(r){case"loading":case"submitting":return i.jsx("div",{className:"w-5 h-5 loading-spinner"});case"selecting":case"captcha":return i.jsx(eM,{className:"w-5 h-5 text-bili-blue"});case"error":return i.jsx(f,{className:"w-5 h-5 text-red-500"});default:return null}})(),i.jsx("h2",{className:"text-2xl font-bold text-gray-900",children:"验证码验证"})]}),i.jsx("p",{className:"text-gray-600",children:(()=>{switch(r){case"loading":return"正在加载...";case"selecting":return"请选择答题分类";case"captcha":return"请输入验证码";case"submitting":return"正在验证...";case"error":return"发生错误";default:return""}})()})]}),"selecting"===r&&(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("div",{children:[i.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-3",children:"选择答题分类"}),i.jsx("p",{className:"text-sm text-gray-600 mb-4",children:"请选择1-3个分类（最多3个），建议选择知识区和历史区以获得更高正确率"}),i.jsx("div",{className:"grid grid-cols-2 gap-3",children:a.map(e=>i.jsx("button",{onClick:()=>_(e.id),disabled:!l.includes(e.id)&&l.length>=3,className:ef("p-3 text-left border rounded-lg transition-colors","hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",l.includes(e.id)?"border-bili-pink bg-bili-pink/5":"border-gray-200"),children:(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[l.includes(e.id)&&i.jsx(p,{className:"w-4 h-4 text-bili-pink"}),i.jsx("span",{className:"text-sm font-medium",children:e.name})]})},e.id))})]}),i.jsx("div",{className:"text-center",children:(0,i.jsxs)("button",{onClick:()=>{if(0===l.length){b("请至少选择一个分类");return}w()},disabled:0===l.length,className:"btn-primary px-6 py-2 disabled:opacity-50 disabled:cursor-not-allowed",children:["确认选择 (",l.length,"/3)"]})})]}),"captcha"===r&&d&&(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("div",{children:[i.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-3",children:"输入验证码"}),(0,i.jsxs)("div",{className:"mb-4",children:[(0,i.jsxs)("div",{className:"flex items-center gap-3 mb-2",children:[i.jsx("span",{className:"text-sm text-gray-600",children:"验证码图片："}),(0,i.jsxs)("button",{onClick:()=>{g(""),w()},className:"btn-outline px-2 py-1 text-xs",children:[i.jsx(m,{className:"w-3 h-3 mr-1"}),"刷新"]})]}),(0,i.jsxs)("a",{href:d.url,target:"_blank",rel:"noopener noreferrer",className:"inline-flex items-center gap-2 p-3 bg-gray-50 border border-gray-200 rounded-lg hover:bg-gray-100 transition-colors",children:[i.jsx(eL,{className:"w-4 h-4 text-bili-blue"}),i.jsx("span",{className:"text-sm text-bili-blue",children:"点击查看验证码"})]})]}),(0,i.jsxs)("div",{children:[i.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"验证码"}),i.jsx("input",{type:"text",value:u,onChange:e=>g(e.target.value),placeholder:"请输入验证码",className:"input",disabled:!1,onKeyPress:e=>{"Enter"===e.key&&v()}})]})]}),i.jsx("div",{className:"text-center",children:i.jsx("button",{onClick:v,disabled:!u.trim(),className:"btn-primary px-6 py-2 disabled:opacity-50 disabled:cursor-not-allowed",children:"提交验证码"})})]}),x&&(0,i.jsxs)("div",{className:"mt-4 p-4 bg-red-50 border border-red-200 rounded-lg",children:[(0,i.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[i.jsx(f,{className:"w-4 h-4 text-red-500"}),i.jsx("span",{className:"text-sm font-medium text-red-800",children:"错误"})]}),i.jsx("p",{className:"text-sm text-red-700 mb-2",children:x}),i.jsx("button",{onClick:()=>{b(""),"error"===r&&0===a.length?y():"error"!==r||d?s("captcha"):w()},className:"btn-outline px-3 py-1 text-sm",children:"重试"})]}),i.jsx("div",{className:"mt-6 p-3 bg-blue-50 border border-blue-200 rounded-lg",children:(0,i.jsxs)("div",{className:"text-sm text-blue-800",children:[i.jsx("p",{className:"font-medium mb-1",children:"提示："}),(0,i.jsxs)("ul",{className:"space-y-1 text-xs",children:[i.jsx("li",{children:"• 验证码用于确认您的答题分类选择"}),i.jsx("li",{children:"• 建议选择知识区和历史区，正确率更高"}),i.jsx("li",{children:"• 每日最多可以答题3次"})]})]})})]})}let eH=l("Brain",[["path",{d:"M9.5 2A2.5 2.5 0 0 1 12 4.5v15a2.5 2.5 0 0 1-4.96.44 2.5 2.5 0 0 1-2.96-3.08 3 3 0 0 1-.34-5.58 2.5 2.5 0 0 1 1.32-4.24 2.5 2.5 0 0 1 1.98-3A2.5 2.5 0 0 1 9.5 2Z",key:"1mhkh5"}],["path",{d:"M14.5 2A2.5 2.5 0 0 0 12 4.5v15a2.5 2.5 0 0 0 4.96.44 2.5 2.5 0 0 0 2.96-3.08 3 3 0 0 0 .34-5.58 2.5 2.5 0 0 0-1.32-4.24 2.5 2.5 0 0 0-1.98-3A2.5 2.5 0 0 0 14.5 2Z",key:"1d6s00"}]]),eY=l("Trophy",[["path",{d:"M6 9H4.5a2.5 2.5 0 0 1 0-5H6",key:"17hqa7"}],["path",{d:"M18 9h1.5a2.5 2.5 0 0 0 0-5H18",key:"lmptdp"}],["path",{d:"M4 22h16",key:"57wxv0"}],["path",{d:"M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22",key:"1nw9bq"}],["path",{d:"M14 14.66V17c0 .55.47.98.97 1.21C16.15 18.75 17 20.24 17 22",key:"1np0yb"}],["path",{d:"M18 2H6v7a6 6 0 0 0 12 0V2Z",key:"u46fv3"}]]),eF=l("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]),eq=l("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]),eG=l("XCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]);function e$({aiConfig:e,onQuizComplete:t,className:r}){let[s,a]=(0,n.useState)("idle"),[o,l]=(0,n.useState)(null),[c,d]=(0,n.useState)(0),[h,u]=(0,n.useState)(0),[f,m]=(0,n.useState)(""),[g,x]=(0,n.useState)(null),[b,y]=(0,n.useState)(""),[w,_]=(0,n.useState)(!0),v=async()=>{try{a("loading"),y(""),m(""),x(null);let e=await eB();if(0!==e.code){if(41103===e.code){y("答题已结束或您已经是硬核会员"),a("completed");return}throw Error(e.message||"获取题目失败")}if(!e.data)throw Error("题目数据为空");l(e.data),d(e.data.question_num),a("answering"),w&&await k(e.data)}catch(e){y(e instanceof Error?e.message:"获取题目失败"),a("error")}},k=async t=>{try{let r=function(e){switch(e.model_choice){case"1":if(!e.api_key_deepseek)throw Error("DeepSeek API密钥未配置");return new eC(e.api_key_deepseek);case"2":if(!e.api_key_gemini)throw Error("Gemini API密钥未配置");return new eT(e.api_key_gemini);case"3":if(!e.openai_config)throw Error("OpenAI配置未完整");return new eI(e.openai_config.base_url,e.openai_config.model,e.openai_config.api_key);default:throw Error("无效的AI模型选择")}}(e),i=function(e){let t=e.answers.map((e,t)=>`${t+1}. ${e.ans_text}`).join("\n");return`题目: ${e.question}
选项:
${t}`}(t),n=await r.ask(i);m(n);let s=function(e,t){let r=e.trim(),i=r.match(/\b([1-4])\b/);if(i){let e=parseInt(i[1]);if(e>=1&&e<=t)return e}let n=r.charAt(0);if(/[1-4]/.test(n)){let e=parseInt(n);if(e>=1&&e<=t)return e}return null}(n,t.answers.length);if(s&&s>=1&&s<=t.answers.length){let e=t.answers[s-1];x(e),w&&setTimeout(()=>{N(t.id,e)},1e3)}else y(`AI回答无效: ${n}`)}catch(e){y(`AI回答失败: ${e instanceof Error?e.message:"未知错误"}`)}},N=async(e,r)=>{try{a("submitting");let i=await eD(e,r.ans_hash,r.ans_text);if(0===i.code){let e=(await ez()).score;e>h&&u(e),setTimeout(v,2e3)}else if(41103===i.code){a("completed");let e=await ez();t(e)}else throw Error(i.message||"提交答案失败")}catch(e){y(e instanceof Error?e.message:"提交答案失败"),a("error")}},j=e=>{o&&"submitting"!==s&&(x(e),w||N(o.id,e))};return(0,i.jsxs)("div",{className:ef("card max-w-4xl mx-auto",r),children:[(0,i.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,i.jsxs)("div",{className:"flex items-center gap-3",children:[(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(()=>{switch(s){case"loading":case"submitting":return i.jsx("div",{className:"w-5 h-5 loading-spinner"});case"answering":return i.jsx(eH,{className:"w-5 h-5 text-bili-blue"});case"completed":return i.jsx(eY,{className:"w-5 h-5 text-yellow-500"});case"error":return i.jsx(eF,{className:"w-5 h-5 text-red-500"});default:return null}})(),i.jsx("h2",{className:"text-xl font-bold text-gray-900",children:c>0?`第${c}题`:"答题系统"})]}),(0,i.jsxs)("div",{className:"text-sm text-gray-500",children:["当前得分: ",i.jsx("span",{className:"font-medium text-bili-pink",children:h})]})]}),i.jsx("div",{className:"flex items-center gap-2",children:(0,i.jsxs)("label",{className:"flex items-center gap-2 text-sm",children:[i.jsx("input",{type:"checkbox",checked:w,onChange:e=>_(e.target.checked),disabled:"submitting"===s,className:"rounded"}),i.jsx("span",{children:"AI自动答题"})]})})]}),i.jsx("div",{className:"mb-4 p-3 bg-gray-50 rounded-lg",children:(0,i.jsxs)("div",{className:"flex items-center gap-2 text-sm text-gray-600",children:[i.jsx(eq,{className:"w-4 h-4"}),i.jsx("span",{children:(()=>{switch(s){case"loading":return"正在获取题目...";case"answering":return w?"AI正在思考...":"请选择答案";case"submitting":return"正在提交答案...";case"completed":return"答题已完成";case"error":return"发生错误";default:return""}})()})]})}),o&&(0,i.jsxs)("div",{className:"mb-6",children:[(0,i.jsxs)("div",{className:"mb-4",children:[i.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"题目"}),i.jsx("p",{className:"text-gray-700 leading-relaxed",children:o.question})]}),(0,i.jsxs)("div",{className:"space-y-3",children:[i.jsx("h4",{className:"text-md font-medium text-gray-900",children:"选项"}),o.answers.map((e,t)=>i.jsx("button",{onClick:()=>j(e),disabled:"submitting"===s||w&&"answering"===s,className:ef("w-full p-4 text-left border rounded-lg transition-colors","hover:bg-gray-50 disabled:cursor-not-allowed",g?.ans_hash===e.ans_hash?"border-bili-pink bg-bili-pink/5":"border-gray-200"),children:(0,i.jsxs)("div",{className:"flex items-start gap-3",children:[i.jsx("span",{className:"flex-shrink-0 w-6 h-6 bg-gray-100 rounded-full flex items-center justify-center text-sm font-medium",children:t+1}),i.jsx("span",{className:"text-gray-700",children:e.ans_text}),g?.ans_hash===e.ans_hash&&i.jsx(p,{className:"flex-shrink-0 w-5 h-5 text-bili-pink ml-auto"})]})},t))]}),f&&(0,i.jsxs)("div",{className:"mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg",children:[(0,i.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[i.jsx(eH,{className:"w-4 h-4 text-blue-600"}),i.jsx("span",{className:"text-sm font-medium text-blue-800",children:"AI回答"})]}),i.jsx("p",{className:"text-sm text-blue-700",children:f})]})]}),b&&(0,i.jsxs)("div",{className:"mb-4 p-4 bg-red-50 border border-red-200 rounded-lg",children:[(0,i.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[i.jsx(eG,{className:"w-4 h-4 text-red-500"}),i.jsx("span",{className:"text-sm font-medium text-red-800",children:"错误"})]}),i.jsx("p",{className:"text-sm text-red-700",children:b}),i.jsx("button",{onClick:()=>{y(""),o?w?k(o):a("answering"):v()},className:"mt-2 btn-outline px-3 py-1 text-sm",children:"重试"})]}),!w&&g&&"answering"===s&&i.jsx("div",{className:"text-center",children:i.jsx("button",{onClick:()=>o&&N(o.id,g),className:"btn-primary px-6 py-2",children:"提交答案"})})]})}let eV=l("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]]),eW=l("RotateCcw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]]),eK=l("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]);function eJ({result:e,onRestart:t,className:r}){let[s,a]=(0,n.useState)(!1),[o,l]=(0,n.useState)(!1),c=e.score>=60,d=e.scores.reduce((e,t)=>e+t.total,0),h=async()=>{try{l(!0),function(){try{em.remove(ex.AUTH_DATA),x=null}catch(e){console.error("清除认证信息失败:",e)}}(),function(){try{em.remove(ex.AI_CONFIG)}catch(e){console.error("清除AI配置失败:",e)}}(),em.clear(),setTimeout(()=>{a(!1),l(!1),window.location.reload()},1e3)}catch(e){console.error("清除数据失败:",e),l(!1)}};return(0,i.jsxs)("div",{className:ef("card max-w-2xl mx-auto",r),children:[(0,i.jsxs)("div",{className:"text-center mb-6",children:[i.jsx("div",{className:"flex items-center justify-center gap-2 mb-4",children:c?i.jsx("div",{className:"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center",children:i.jsx(eY,{className:"w-8 h-8 text-green-600"})}):i.jsx("div",{className:"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center",children:i.jsx(eG,{className:"w-8 h-8 text-red-600"})})}),i.jsx("h2",{className:"text-3xl font-bold text-gray-900 mb-2",children:c?"\uD83C\uDF89 恭喜通过！":"\uD83D\uDE14 未能通过"}),i.jsx("p",{className:"text-gray-600",children:c?"您已成功通过哔哩哔哩硬核会员答题！":"运气稍微有点差，建议重新答题，知识区和历史区的正确率会更高"})]}),(0,i.jsxs)("div",{className:"mb-6",children:[(0,i.jsxs)("div",{className:"text-center mb-4",children:[i.jsx("div",{className:"text-4xl font-bold text-bili-pink mb-1",children:e.score}),(0,i.jsxs)("div",{className:"text-sm text-gray-500",children:["总分 / ",d]})]}),i.jsx("div",{className:"w-full bg-gray-200 rounded-full h-3 mb-4",children:i.jsx("div",{className:ef("h-3 rounded-full transition-all duration-1000",c?"bg-green-500":"bg-red-500"),style:{width:`${Math.min(e.score/d*100,100)}%`}})}),(0,i.jsxs)("div",{className:"flex justify-between text-xs text-gray-500 mb-4",children:[i.jsx("span",{children:"0"}),(0,i.jsxs)("span",{className:"relative",children:["60 (及格线)",i.jsx("div",{className:"absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-6",children:i.jsx("div",{className:"w-px h-4 bg-yellow-400"})})]}),i.jsx("span",{children:d})]})]}),(0,i.jsxs)("div",{className:"mb-6",children:[i.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-3",children:"分类得分详情"}),i.jsx("div",{className:"space-y-3",children:e.scores.map((e,t)=>(0,i.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[i.jsx(eV,{className:"w-4 h-4 text-yellow-500"}),i.jsx("span",{className:"font-medium text-gray-900",children:e.category})]}),(0,i.jsxs)("div",{className:"text-right",children:[(0,i.jsxs)("div",{className:"font-medium text-gray-900",children:[e.score," / ",e.total]}),(0,i.jsxs)("div",{className:"text-xs text-gray-500",children:[(e.score/e.total*100).toFixed(1),"%"]})]})]},t))})]}),(0,i.jsxs)("div",{className:"space-y-3",children:[(0,i.jsxs)("button",{onClick:t,className:"w-full btn-primary py-3",children:[i.jsx(eW,{className:"w-4 h-4 mr-2"}),"重新答题"]}),c&&(0,i.jsxs)("button",{onClick:()=>a(!0),className:"w-full btn-outline py-3",children:[i.jsx(eK,{className:"w-4 h-4 mr-2"}),"清除登录信息和API密钥"]})]}),i.jsx("div",{className:"mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg",children:(0,i.jsxs)("div",{className:"text-sm text-blue-800",children:[i.jsx("p",{className:"font-medium mb-2",children:c?"\uD83C\uDF8A 成功提示：":"\uD83D\uDCA1 重试建议："}),c?(0,i.jsxs)("ul",{className:"space-y-1 text-xs",children:[i.jsx("li",{children:"• 您已成功获得哔哩哔哩硬核会员资格"}),i.jsx("li",{children:"• 为了保护您的隐私，建议清除本地保存的登录信息"}),i.jsx("li",{children:"• 感谢使用本工具，祝您使用愉快！"})]}):(0,i.jsxs)("ul",{className:"space-y-1 text-xs",children:[i.jsx("li",{children:"• 建议选择知识区和历史区，这些分类的正确率更高"}),i.jsx("li",{children:"• 每日最多可以答题3次，请合理安排"}),i.jsx("li",{children:"• 可以尝试更换不同的AI模型来提高准确率"})]})]})}),s&&i.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,i.jsxs)("div",{className:"bg-white rounded-lg p-6 max-w-md mx-4",children:[(0,i.jsxs)("div",{className:"text-center mb-4",children:[i.jsx("div",{className:"w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-3",children:i.jsx(eK,{className:"w-6 h-6 text-yellow-600"})}),i.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"确认清除数据"}),i.jsx("p",{className:"text-sm text-gray-600",children:"此操作将清除所有本地保存的登录信息和API密钥，确保您的隐私安全。此操作不可撤销。"})]}),(0,i.jsxs)("div",{className:"flex gap-3",children:[i.jsx("button",{onClick:()=>a(!1),disabled:o,className:"flex-1 btn-outline py-2 disabled:opacity-50",children:"取消"}),i.jsx("button",{onClick:h,disabled:o,className:"flex-1 btn-primary py-2 disabled:opacity-50",children:o?(0,i.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[i.jsx("div",{className:"w-4 h-4 loading-spinner border-white"}),i.jsx("span",{children:"清除中..."})]}):"确认清除"})]})]})})]})}class eZ extends s().Component{constructor(e){super(e),this.resetError=()=>{this.setState({hasError:!1,error:void 0})},this.state={hasError:!1}}static getDerivedStateFromError(e){return{hasError:!0,error:e}}componentDidCatch(e,t){console.error("ErrorBoundary caught an error:",e,t)}render(){if(this.state.hasError){if(this.props.fallback){let e=this.props.fallback;return i.jsx(e,{error:this.state.error,resetError:this.resetError})}return i.jsx(eQ,{error:this.state.error,resetError:this.resetError})}return this.props.children}}function eQ({error:e,resetError:t}){return i.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,i.jsxs)("div",{className:"max-w-md mx-auto text-center p-6",children:[i.jsx("div",{className:"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4",children:i.jsx(eF,{className:"w-8 h-8 text-red-600"})}),i.jsx("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:"出现了一些问题"}),i.jsx("p",{className:"text-gray-600 mb-6",children:"应用遇到了意外错误，请尝试刷新页面或重新开始。"}),e&&(0,i.jsxs)("details",{className:"mb-6 text-left",children:[i.jsx("summary",{className:"cursor-pointer text-sm text-gray-500 hover:text-gray-700",children:"查看错误详情"}),(0,i.jsxs)("pre",{className:"mt-2 p-3 bg-gray-100 rounded text-xs text-gray-700 overflow-auto",children:[e.message,e.stack&&"\n\n"+e.stack]})]}),(0,i.jsxs)("div",{className:"space-y-3",children:[(0,i.jsxs)("button",{onClick:t,className:"w-full btn-primary py-2",children:[i.jsx(m,{className:"w-4 h-4 mr-2"}),"重试"]}),i.jsx("button",{onClick:()=>window.location.reload(),className:"w-full btn-outline py-2",children:"刷新页面"})]}),i.jsx("p",{className:"mt-6 text-xs text-gray-500",children:"如果问题持续存在，请尝试清除浏览器缓存或联系技术支持。"})]})})}function eX({size:e="md",className:t,text:r}){return i.jsx("div",{className:ef("flex items-center justify-center",t),children:(0,i.jsxs)("div",{className:"text-center",children:[i.jsx("div",{className:ef("loading-spinner mx-auto",{sm:"w-4 h-4",md:"w-6 h-6",lg:"w-8 h-8"}[e])}),r&&i.jsx("p",{className:"mt-2 text-sm text-gray-600",children:r})]})})}function e0(){let[e,t]=(0,n.useState)("login"),[r,s]=(0,n.useState)(null),[a,o]=(0,n.useState)(null),[l,u]=(0,n.useState)(null),[p,f]=(0,n.useState)(!0);return p?i.jsx("div",{className:"min-h-screen flex items-center justify-center",children:i.jsx(eX,{size:"lg",text:"正在初始化..."})}):i.jsx(eZ,{children:(0,i.jsxs)("div",{className:"min-h-screen",children:[i.jsx("header",{className:"bg-white/80 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-40",children:i.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,i.jsxs)("div",{className:"flex items-center justify-between h-16",children:[(0,i.jsxs)("div",{className:"flex items-center gap-3",children:[i.jsx("div",{className:"w-8 h-8 bg-bili-pink rounded-lg flex items-center justify-center",children:i.jsx(c,{className:"w-5 h-5 text-white"})}),(0,i.jsxs)("div",{children:[i.jsx("h1",{className:"text-lg font-bold text-gray-900",children:"哔哩哔哩硬核会员答题助手"}),i.jsx("p",{className:"text-xs text-gray-500",children:"基于AI的自动答题工具"})]})]}),i.jsx("div",{className:"flex items-center gap-4",children:(0,i.jsxs)("a",{href:"https://github.com/Karben233/bili-hardcore",target:"_blank",rel:"noopener noreferrer",className:"flex items-center gap-2 text-sm text-gray-600 hover:text-gray-900 transition-colors",children:[i.jsx(d,{className:"w-4 h-4"}),i.jsx("span",{className:"hidden sm:inline",children:"GitHub"})]})})]})})}),(0,i.jsxs)("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[i.jsx("div",{className:"mb-8",children:i.jsx("div",{className:"flex items-center justify-center",children:i.jsx("div",{className:"flex items-center space-x-4",children:[{key:"login",label:"登录",step:1},{key:"ai-config",label:"AI配置",step:2},{key:"quiz",label:"答题",step:3},{key:"result",label:"结果",step:4}].map((t,r)=>(0,i.jsxs)("div",{className:"flex items-center",children:[i.jsx("div",{className:`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${e===t.key?"bg-bili-pink text-white":["login","ai-config","quiz"].indexOf(e)>["login","ai-config","quiz"].indexOf(t.key)?"bg-green-500 text-white":"bg-gray-200 text-gray-600"}`,children:t.step}),i.jsx("span",{className:`ml-2 text-sm ${e===t.key?"text-bili-pink font-medium":"text-gray-500"}`,children:t.label}),r<3&&i.jsx("div",{className:"w-8 h-px bg-gray-300 mx-4"})]},t.key))})})}),(0,i.jsxs)("div",{className:"animate-fade-in",children:["login"===e&&i.jsx(ev,{onLoginSuccess:e=>{s(e),x=e,t("ai-config")}}),"ai-config"===e&&i.jsx(eP,{onConfigComplete:e=>{o(e),t("quiz")}}),"captcha"===e&&i.jsx(eU,{onCaptchaComplete:e=>{t("quiz")}}),"quiz"===e&&a&&i.jsx(e$,{aiConfig:a,onQuizComplete:e=>{u(e),t("result")}}),"result"===e&&l&&i.jsx(eJ,{result:l,onRestart:()=>{u(null),t("quiz")}})]})]}),i.jsx("footer",{className:"bg-white/80 backdrop-blur-sm border-t border-gray-200 mt-16",children:i.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsxs)("div",{className:"flex items-center justify-center gap-2 mb-4",children:[i.jsx(h,{className:"w-4 h-4 text-red-500"}),i.jsx("span",{className:"text-sm text-gray-600",children:"本软件免费且代码开源"})]}),(0,i.jsxs)("div",{className:"space-y-2 text-xs text-gray-500",children:[(0,i.jsxs)("p",{children:["源码地址：",i.jsx("a",{href:"https://github.com/Karben233/bili-hardcore",target:"_blank",rel:"noopener noreferrer",className:"text-bili-blue hover:underline ml-1",children:"https://github.com/Karben233/bili-hardcore"})]}),i.jsx("p",{children:"问题反馈：请在GitHub仓库中提交Issue"}),i.jsx("p",{className:"text-gray-400",children:"免责声明：本工具仅供学习交流使用，请遵守相关平台的使用条款"})]})]})})})]})})}},2029:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o,metadata:()=>a});var i=r(9510),n=r(5384),s=r.n(n);r(5023);let a={title:"哔哩哔哩硬核会员答题助手",description:"基于AI的哔哩哔哩硬核会员自动答题工具，支持DeepSeek、Gemini、OpenAI等多种AI模型",keywords:["哔哩哔哩","硬核会员","答题","AI","DeepSeek","Gemini","OpenAI"],authors:[{name:"Bili Hardcore Team"}],icons:{icon:"/favicon.ico"},openGraph:{title:"哔哩哔哩硬核会员答题助手",description:"基于AI的哔哩哔哩硬核会员自动答题工具",type:"website",locale:"zh_CN"}};function o({children:e}){return i.jsx("html",{lang:"zh-CN",children:i.jsx("body",{className:s().className,children:i.jsx("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-pink-50",children:e})})})}},5480:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});let i=(0,r(8570).createProxy)(String.raw`E:\Temp\bili-hardcore\src\app\page.tsx#default`)},5023:()=>{}};var t=require("../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[935],()=>r(6739));module.exports=i})();