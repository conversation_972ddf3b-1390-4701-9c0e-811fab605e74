(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[997],{6946:function(t){"use strict";var e={single_source_shortest_paths:function(t,r,o){var n,i,l,a,s,c,u,d={},f={};f[r]=0;var h=e.PriorityQueue.make();for(h.push(r,0);!h.empty();)for(l in i=(n=h.pop()).value,a=n.cost,s=t[i]||{})s.hasOwnProperty(l)&&(c=a+s[l],u=f[l],(void 0===f[l]||u>c)&&(f[l]=c,h.push(l,c),d[l]=i));if(void 0!==o&&void 0===f[o])throw Error(["Could not find a path from ",r," to ",o,"."].join(""));return d},extract_shortest_path_from_predecessor_list:function(t,e){for(var r=[],o=e;o;)r.push(o),t[o],o=t[o];return r.reverse(),r},find_path:function(t,r,o){var n=e.single_source_shortest_paths(t,r,o);return e.extract_shortest_path_from_predecessor_list(n,o)},PriorityQueue:{make:function(t){var r,o=e.PriorityQueue,n={};for(r in t=t||{},o)o.hasOwnProperty(r)&&(n[r]=o[r]);return n.queue=[],n.sorter=t.sorter||o.default_sorter,n},default_sorter:function(t,e){return t.cost-e.cost},push:function(t,e){this.queue.push({value:t,cost:e}),this.queue.sort(this.sorter)},pop:function(){return this.queue.shift()},empty:function(){return 0===this.queue.length}}};t.exports=e},9763:function(t,e,r){"use strict";r.d(e,{Z:function(){return l}});var o=r(2265),n={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let i=t=>t.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),l=(t,e)=>{let r=(0,o.forwardRef)((r,l)=>{let{color:a="currentColor",size:s=24,strokeWidth:c=2,absoluteStrokeWidth:u,className:d="",children:f,...h}=r;return(0,o.createElement)("svg",{ref:l,...n,width:s,height:s,stroke:a,strokeWidth:u?24*Number(c)/Number(s):c,className:["lucide","lucide-".concat(i(t)),d].join(" "),...h},[...e.map(t=>{let[e,r]=t;return(0,o.createElement)(e,r)}),...Array.isArray(f)?f:[f]])});return r.displayName="".concat(t),r}},2252:function(t,e,r){"use strict";r.d(e,{Z:function(){return o}});let o=(0,r(9763).Z)("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},3639:function(t,e,r){"use strict";r.d(e,{Z:function(){return o}});let o=(0,r(9763).Z)("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},2222:function(t,e,r){"use strict";r.d(e,{Z:function(){return o}});let o=(0,r(9763).Z)("Bot",[["path",{d:"M12 8V4H8",key:"hb8ula"}],["rect",{width:"16",height:"12",x:"4",y:"8",rx:"2",key:"enze0r"}],["path",{d:"M2 14h2",key:"vft8re"}],["path",{d:"M20 14h2",key:"4cs60a"}],["path",{d:"M15 13v2",key:"1xurst"}],["path",{d:"M9 13v2",key:"rq6x2g"}]])},4972:function(t,e,r){"use strict";r.d(e,{Z:function(){return o}});let o=(0,r(9763).Z)("Brain",[["path",{d:"M9.5 2A2.5 2.5 0 0 1 12 4.5v15a2.5 2.5 0 0 1-4.96.44 2.5 2.5 0 0 1-2.96-3.08 3 3 0 0 1-.34-5.58 2.5 2.5 0 0 1 1.32-4.24 2.5 2.5 0 0 1 1.98-3A2.5 2.5 0 0 1 9.5 2Z",key:"1mhkh5"}],["path",{d:"M14.5 2A2.5 2.5 0 0 0 12 4.5v15a2.5 2.5 0 0 0 4.96.44 2.5 2.5 0 0 0 2.96-3.08 3 3 0 0 0 .34-5.58 2.5 2.5 0 0 0-1.32-4.24 2.5 2.5 0 0 0-1.98-3A2.5 2.5 0 0 0 14.5 2Z",key:"1d6s00"}]])},5302:function(t,e,r){"use strict";r.d(e,{Z:function(){return o}});let o=(0,r(9763).Z)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},1723:function(t,e,r){"use strict";r.d(e,{Z:function(){return o}});let o=(0,r(9763).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},6362:function(t,e,r){"use strict";r.d(e,{Z:function(){return o}});let o=(0,r(9763).Z)("ExternalLink",[["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}],["polyline",{points:"15 3 21 3 21 9",key:"mznyad"}],["line",{x1:"10",x2:"21",y1:"14",y2:"3",key:"18c3s4"}]])},7769:function(t,e,r){"use strict";r.d(e,{Z:function(){return o}});let o=(0,r(9763).Z)("EyeOff",[["path",{d:"M9.88 9.88a3 3 0 1 0 4.24 4.24",key:"1jxqfv"}],["path",{d:"M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68",key:"9wicm4"}],["path",{d:"M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61",key:"1jreej"}],["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}]])},2208:function(t,e,r){"use strict";r.d(e,{Z:function(){return o}});let o=(0,r(9763).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},5135:function(t,e,r){"use strict";r.d(e,{Z:function(){return o}});let o=(0,r(9763).Z)("Github",[["path",{d:"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4",key:"tonef"}],["path",{d:"M9 18c-4.51 2-5-2-7-2",key:"9comsn"}]])},8997:function(t,e,r){"use strict";r.d(e,{Z:function(){return o}});let o=(0,r(9763).Z)("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},7404:function(t,e,r){"use strict";r.d(e,{Z:function(){return o}});let o=(0,r(9763).Z)("QrCode",[["rect",{width:"5",height:"5",x:"3",y:"3",rx:"1",key:"1tu5fj"}],["rect",{width:"5",height:"5",x:"16",y:"3",rx:"1",key:"1v8r4q"}],["rect",{width:"5",height:"5",x:"3",y:"16",rx:"1",key:"1x03jg"}],["path",{d:"M21 16h-3a2 2 0 0 0-2 2v3",key:"177gqh"}],["path",{d:"M21 21v.01",key:"ents32"}],["path",{d:"M12 7v3a2 2 0 0 1-2 2H7",key:"8crl2c"}],["path",{d:"M3 12h.01",key:"nlz23k"}],["path",{d:"M12 3h.01",key:"n36tog"}],["path",{d:"M12 16v.01",key:"133mhm"}],["path",{d:"M16 12h1",key:"1slzba"}],["path",{d:"M21 12v.01",key:"1lwtk9"}],["path",{d:"M12 21v-1",key:"1880an"}]])},7168:function(t,e,r){"use strict";r.d(e,{Z:function(){return o}});let o=(0,r(9763).Z)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},9076:function(t,e,r){"use strict";r.d(e,{Z:function(){return o}});let o=(0,r(9763).Z)("RotateCcw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]])},8728:function(t,e,r){"use strict";r.d(e,{Z:function(){return o}});let o=(0,r(9763).Z)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},8906:function(t,e,r){"use strict";r.d(e,{Z:function(){return o}});let o=(0,r(9763).Z)("Shield",[["path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10",key:"1irkt0"}]])},6595:function(t,e,r){"use strict";r.d(e,{Z:function(){return o}});let o=(0,r(9763).Z)("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]])},8930:function(t,e,r){"use strict";r.d(e,{Z:function(){return o}});let o=(0,r(9763).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},9474:function(t,e,r){"use strict";r.d(e,{Z:function(){return o}});let o=(0,r(9763).Z)("Trophy",[["path",{d:"M6 9H4.5a2.5 2.5 0 0 1 0-5H6",key:"17hqa7"}],["path",{d:"M18 9h1.5a2.5 2.5 0 0 0 0-5H18",key:"lmptdp"}],["path",{d:"M4 22h16",key:"57wxv0"}],["path",{d:"M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22",key:"1nw9bq"}],["path",{d:"M14 14.66V17c0 .55.47.98.97 1.21C16.15 18.75 17 20.24 17 22",key:"1np0yb"}],["path",{d:"M18 2H6v7a6 6 0 0 0 12 0V2Z",key:"u46fv3"}]])},5131:function(t,e,r){"use strict";r.d(e,{Z:function(){return o}});let o=(0,r(9763).Z)("XCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},1239:function(t,e,r){"use strict";r.d(e,{Z:function(){return o}});let o=(0,r(9763).Z)("Zap",[["polygon",{points:"13 2 3 14 12 14 11 22 21 10 12 10 13 2",key:"45s27k"}]])},5819:function(t,e,r){let o=r(4888),n=r(2216),i=r(962),l=r(5623);function a(t,e,r,i,l){let a=[].slice.call(arguments,1),s=a.length,c="function"==typeof a[s-1];if(!c&&!o())throw Error("Callback required as last argument");if(c){if(s<2)throw Error("Too few arguments provided");2===s?(l=r,r=e,e=i=void 0):3===s&&(e.getContext&&void 0===l?(l=i,i=void 0):(l=i,i=r,r=e,e=void 0))}else{if(s<1)throw Error("Too few arguments provided");return 1===s?(r=e,e=i=void 0):2!==s||e.getContext||(i=r,r=e,e=void 0),new Promise(function(o,l){try{let l=n.create(r,i);o(t(l,e,i))}catch(t){l(t)}})}try{let o=n.create(r,i);l(null,t(o,e,i))}catch(t){l(t)}}e.create=n.create,e.toCanvas=a.bind(null,i.render),e.toDataURL=a.bind(null,i.renderToDataURL),e.toString=a.bind(null,function(t,e,r){return l.render(t,r)})},4888:function(t){t.exports=function(){return"function"==typeof Promise&&Promise.prototype&&Promise.prototype.then}},4691:function(t,e,r){let o=r(2415).getSymbolSize;e.getRowColCoords=function(t){if(1===t)return[];let e=Math.floor(t/7)+2,r=o(t),n=145===r?26:2*Math.ceil((r-13)/(2*e-2)),i=[r-7];for(let t=1;t<e-1;t++)i[t]=i[t-1]-n;return i.push(6),i.reverse()},e.getPositions=function(t){let r=[],o=e.getRowColCoords(t),n=o.length;for(let t=0;t<n;t++)for(let e=0;e<n;e++)(0!==t||0!==e)&&(0!==t||e!==n-1)&&(t!==n-1||0!==e)&&r.push([o[t],o[e]]);return r}},7111:function(t,e,r){let o=r(7956),n=["0","1","2","3","4","5","6","7","8","9","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"," ","$","%","*","+","-",".","/",":"];function i(t){this.mode=o.ALPHANUMERIC,this.data=t}i.getBitsLength=function(t){return 11*Math.floor(t/2)+t%2*6},i.prototype.getLength=function(){return this.data.length},i.prototype.getBitsLength=function(){return i.getBitsLength(this.data.length)},i.prototype.write=function(t){let e;for(e=0;e+2<=this.data.length;e+=2){let r=45*n.indexOf(this.data[e]);r+=n.indexOf(this.data[e+1]),t.put(r,11)}this.data.length%2&&t.put(n.indexOf(this.data[e]),6)},t.exports=i},9298:function(t){function e(){this.buffer=[],this.length=0}e.prototype={get:function(t){return(this.buffer[Math.floor(t/8)]>>>7-t%8&1)==1},put:function(t,e){for(let r=0;r<e;r++)this.putBit((t>>>e-r-1&1)==1)},getLengthInBits:function(){return this.length},putBit:function(t){let e=Math.floor(this.length/8);this.buffer.length<=e&&this.buffer.push(0),t&&(this.buffer[e]|=128>>>this.length%8),this.length++}},t.exports=e},5526:function(t){function e(t){if(!t||t<1)throw Error("BitMatrix size must be defined and greater than 0");this.size=t,this.data=new Uint8Array(t*t),this.reservedBit=new Uint8Array(t*t)}e.prototype.set=function(t,e,r,o){let n=t*this.size+e;this.data[n]=r,o&&(this.reservedBit[n]=!0)},e.prototype.get=function(t,e){return this.data[t*this.size+e]},e.prototype.xor=function(t,e,r){this.data[t*this.size+e]^=r},e.prototype.isReserved=function(t,e){return this.reservedBit[t*this.size+e]},t.exports=e},8153:function(t,e,r){let o=r(7956);function n(t){this.mode=o.BYTE,"string"==typeof t?this.data=new TextEncoder().encode(t):this.data=new Uint8Array(t)}n.getBitsLength=function(t){return 8*t},n.prototype.getLength=function(){return this.data.length},n.prototype.getBitsLength=function(){return n.getBitsLength(this.data.length)},n.prototype.write=function(t){for(let e=0,r=this.data.length;e<r;e++)t.put(this.data[e],8)},t.exports=n},9386:function(t,e,r){let o=r(9947),n=[1,1,1,1,1,1,1,1,1,1,2,2,1,2,2,4,1,2,4,4,2,4,4,4,2,4,6,5,2,4,6,6,2,5,8,8,4,5,8,8,4,5,8,11,4,8,10,11,4,9,12,16,4,9,16,16,6,10,12,18,6,10,17,16,6,11,16,19,6,13,18,21,7,14,21,25,8,16,20,25,8,17,23,25,9,17,23,34,9,18,25,30,10,20,27,32,12,21,29,35,12,23,34,37,12,25,34,40,13,26,35,42,14,28,38,45,15,29,40,48,16,31,43,51,17,33,45,54,18,35,48,57,19,37,51,60,19,38,53,63,20,40,56,66,21,43,59,70,22,45,62,74,24,47,65,77,25,49,68,81],i=[7,10,13,17,10,16,22,28,15,26,36,44,20,36,52,64,26,48,72,88,36,64,96,112,40,72,108,130,48,88,132,156,60,110,160,192,72,130,192,224,80,150,224,264,96,176,260,308,104,198,288,352,120,216,320,384,132,240,360,432,144,280,408,480,168,308,448,532,180,338,504,588,196,364,546,650,224,416,600,700,224,442,644,750,252,476,690,816,270,504,750,900,300,560,810,960,312,588,870,1050,336,644,952,1110,360,700,1020,1200,390,728,1050,1260,420,784,1140,1350,450,812,1200,1440,480,868,1290,1530,510,924,1350,1620,540,980,1440,1710,570,1036,1530,1800,570,1064,1590,1890,600,1120,1680,1980,630,1204,1770,2100,660,1260,1860,2220,720,1316,1950,2310,750,1372,2040,2430];e.getBlocksCount=function(t,e){switch(e){case o.L:return n[(t-1)*4+0];case o.M:return n[(t-1)*4+1];case o.Q:return n[(t-1)*4+2];case o.H:return n[(t-1)*4+3];default:return}},e.getTotalCodewordsCount=function(t,e){switch(e){case o.L:return i[(t-1)*4+0];case o.M:return i[(t-1)*4+1];case o.Q:return i[(t-1)*4+2];case o.H:return i[(t-1)*4+3];default:return}}},9947:function(t,e){e.L={bit:1},e.M={bit:0},e.Q={bit:3},e.H={bit:2},e.isValid=function(t){return t&&void 0!==t.bit&&t.bit>=0&&t.bit<4},e.from=function(t,r){if(e.isValid(t))return t;try{return function(t){if("string"!=typeof t)throw Error("Param is not a string");switch(t.toLowerCase()){case"l":case"low":return e.L;case"m":case"medium":return e.M;case"q":case"quartile":return e.Q;case"h":case"high":return e.H;default:throw Error("Unknown EC Level: "+t)}}(t)}catch(t){return r}}},4086:function(t,e,r){let o=r(2415).getSymbolSize;e.getPositions=function(t){let e=o(t);return[[0,0],[e-7,0],[0,e-7]]}},7433:function(t,e,r){let o=r(2415),n=o.getBCHDigit(1335);e.getEncodedBits=function(t,e){let r=t.bit<<3|e,i=r<<10;for(;o.getBCHDigit(i)-n>=0;)i^=1335<<o.getBCHDigit(i)-n;return(r<<10|i)^21522}},3384:function(t,e){let r=new Uint8Array(512),o=new Uint8Array(256);!function(){let t=1;for(let e=0;e<255;e++)r[e]=t,o[t]=e,256&(t<<=1)&&(t^=285);for(let t=255;t<512;t++)r[t]=r[t-255]}(),e.log=function(t){if(t<1)throw Error("log("+t+")");return o[t]},e.exp=function(t){return r[t]},e.mul=function(t,e){return 0===t||0===e?0:r[o[t]+o[e]]}},8514:function(t,e,r){let o=r(7956),n=r(2415);function i(t){this.mode=o.KANJI,this.data=t}i.getBitsLength=function(t){return 13*t},i.prototype.getLength=function(){return this.data.length},i.prototype.getBitsLength=function(){return i.getBitsLength(this.data.length)},i.prototype.write=function(t){let e;for(e=0;e<this.data.length;e++){let r=n.toSJIS(this.data[e]);if(r>=33088&&r<=40956)r-=33088;else if(r>=57408&&r<=60351)r-=49472;else throw Error("Invalid SJIS character: "+this.data[e]+"\nMake sure your charset is UTF-8");r=(r>>>8&255)*192+(255&r),t.put(r,13)}},t.exports=i},4483:function(t,e){e.Patterns={PATTERN000:0,PATTERN001:1,PATTERN010:2,PATTERN011:3,PATTERN100:4,PATTERN101:5,PATTERN110:6,PATTERN111:7};let r={N1:3,N2:3,N3:40,N4:10};e.isValid=function(t){return null!=t&&""!==t&&!isNaN(t)&&t>=0&&t<=7},e.from=function(t){return e.isValid(t)?parseInt(t,10):void 0},e.getPenaltyN1=function(t){let e=t.size,o=0,n=0,i=0,l=null,a=null;for(let s=0;s<e;s++){n=i=0,l=a=null;for(let c=0;c<e;c++){let e=t.get(s,c);e===l?n++:(n>=5&&(o+=r.N1+(n-5)),l=e,n=1),(e=t.get(c,s))===a?i++:(i>=5&&(o+=r.N1+(i-5)),a=e,i=1)}n>=5&&(o+=r.N1+(n-5)),i>=5&&(o+=r.N1+(i-5))}return o},e.getPenaltyN2=function(t){let e=t.size,o=0;for(let r=0;r<e-1;r++)for(let n=0;n<e-1;n++){let e=t.get(r,n)+t.get(r,n+1)+t.get(r+1,n)+t.get(r+1,n+1);(4===e||0===e)&&o++}return o*r.N2},e.getPenaltyN3=function(t){let e=t.size,o=0,n=0,i=0;for(let r=0;r<e;r++){n=i=0;for(let l=0;l<e;l++)n=n<<1&2047|t.get(r,l),l>=10&&(1488===n||93===n)&&o++,i=i<<1&2047|t.get(l,r),l>=10&&(1488===i||93===i)&&o++}return o*r.N3},e.getPenaltyN4=function(t){let e=0,o=t.data.length;for(let r=0;r<o;r++)e+=t.data[r];return Math.abs(Math.ceil(100*e/o/5)-10)*r.N4},e.applyMask=function(t,r){let o=r.size;for(let n=0;n<o;n++)for(let i=0;i<o;i++)r.isReserved(i,n)||r.xor(i,n,function(t,r,o){switch(t){case e.Patterns.PATTERN000:return(r+o)%2==0;case e.Patterns.PATTERN001:return r%2==0;case e.Patterns.PATTERN010:return o%3==0;case e.Patterns.PATTERN011:return(r+o)%3==0;case e.Patterns.PATTERN100:return(Math.floor(r/2)+Math.floor(o/3))%2==0;case e.Patterns.PATTERN101:return r*o%2+r*o%3==0;case e.Patterns.PATTERN110:return(r*o%2+r*o%3)%2==0;case e.Patterns.PATTERN111:return(r*o%3+(r+o)%2)%2==0;default:throw Error("bad maskPattern:"+t)}}(t,i,n))},e.getBestMask=function(t,r){let o=Object.keys(e.Patterns).length,n=0,i=1/0;for(let l=0;l<o;l++){r(l),e.applyMask(l,t);let o=e.getPenaltyN1(t)+e.getPenaltyN2(t)+e.getPenaltyN3(t)+e.getPenaltyN4(t);e.applyMask(l,t),o<i&&(i=o,n=l)}return n}},7956:function(t,e,r){let o=r(6623),n=r(581);e.NUMERIC={id:"Numeric",bit:1,ccBits:[10,12,14]},e.ALPHANUMERIC={id:"Alphanumeric",bit:2,ccBits:[9,11,13]},e.BYTE={id:"Byte",bit:4,ccBits:[8,16,16]},e.KANJI={id:"Kanji",bit:8,ccBits:[8,10,12]},e.MIXED={bit:-1},e.getCharCountIndicator=function(t,e){if(!t.ccBits)throw Error("Invalid mode: "+t);if(!o.isValid(e))throw Error("Invalid version: "+e);return e>=1&&e<10?t.ccBits[0]:e<27?t.ccBits[1]:t.ccBits[2]},e.getBestModeForData=function(t){return n.testNumeric(t)?e.NUMERIC:n.testAlphanumeric(t)?e.ALPHANUMERIC:n.testKanji(t)?e.KANJI:e.BYTE},e.toString=function(t){if(t&&t.id)return t.id;throw Error("Invalid mode")},e.isValid=function(t){return t&&t.bit&&t.ccBits},e.from=function(t,r){if(e.isValid(t))return t;try{return function(t){if("string"!=typeof t)throw Error("Param is not a string");switch(t.toLowerCase()){case"numeric":return e.NUMERIC;case"alphanumeric":return e.ALPHANUMERIC;case"kanji":return e.KANJI;case"byte":return e.BYTE;default:throw Error("Unknown mode: "+t)}}(t)}catch(t){return r}}},1687:function(t,e,r){let o=r(7956);function n(t){this.mode=o.NUMERIC,this.data=t.toString()}n.getBitsLength=function(t){return 10*Math.floor(t/3)+(t%3?t%3*3+1:0)},n.prototype.getLength=function(){return this.data.length},n.prototype.getBitsLength=function(){return n.getBitsLength(this.data.length)},n.prototype.write=function(t){let e,r;for(e=0;e+3<=this.data.length;e+=3)r=parseInt(this.data.substr(e,3),10),t.put(r,10);let o=this.data.length-e;o>0&&(r=parseInt(this.data.substr(e),10),t.put(r,3*o+1))},t.exports=n},6413:function(t,e,r){let o=r(3384);e.mul=function(t,e){let r=new Uint8Array(t.length+e.length-1);for(let n=0;n<t.length;n++)for(let i=0;i<e.length;i++)r[n+i]^=o.mul(t[n],e[i]);return r},e.mod=function(t,e){let r=new Uint8Array(t);for(;r.length-e.length>=0;){let t=r[0];for(let n=0;n<e.length;n++)r[n]^=o.mul(e[n],t);let n=0;for(;n<r.length&&0===r[n];)n++;r=r.slice(n)}return r},e.generateECPolynomial=function(t){let r=new Uint8Array([1]);for(let n=0;n<t;n++)r=e.mul(r,new Uint8Array([1,o.exp(n)]));return r}},2216:function(t,e,r){let o=r(2415),n=r(9947),i=r(9298),l=r(5526),a=r(4691),s=r(4086),c=r(4483),u=r(9386),d=r(4011),f=r(3379),h=r(7433),p=r(7956),g=r(9940);function m(t,e,r){let o,n;let i=t.size,l=h.getEncodedBits(e,r);for(o=0;o<15;o++)n=(l>>o&1)==1,o<6?t.set(o,8,n,!0):o<8?t.set(o+1,8,n,!0):t.set(i-15+o,8,n,!0),o<8?t.set(8,i-o-1,n,!0):o<9?t.set(8,15-o-1+1,n,!0):t.set(8,15-o-1,n,!0);t.set(i-8,8,1,!0)}e.create=function(t,e){let r,h;if(void 0===t||""===t)throw Error("No input text");let b=n.M;return void 0!==e&&(b=n.from(e.errorCorrectionLevel,n.M),r=f.from(e.version),h=c.from(e.maskPattern),e.toSJISFunc&&o.setToSJISFunction(e.toSJISFunc)),function(t,e,r,n){let h;if(Array.isArray(t))h=g.fromArray(t);else if("string"==typeof t){let o=e;if(!o){let e=g.rawSplit(t);o=f.getBestVersionForData(e,r)}h=g.fromString(t,o||40)}else throw Error("Invalid data");let b=f.getBestVersionForData(h,r);if(!b)throw Error("The amount of data is too big to be stored in a QR Code");if(e){if(e<b)throw Error("\nThe chosen QR Code version cannot contain this amount of data.\nMinimum version required to store current data is: "+b+".\n")}else e=b;let y=function(t,e,r){let n=new i;r.forEach(function(e){n.put(e.mode.bit,4),n.put(e.getLength(),p.getCharCountIndicator(e.mode,t)),e.write(n)});let l=(o.getSymbolTotalCodewords(t)-u.getTotalCodewordsCount(t,e))*8;for(n.getLengthInBits()+4<=l&&n.put(0,4);n.getLengthInBits()%8!=0;)n.putBit(0);let a=(l-n.getLengthInBits())/8;for(let t=0;t<a;t++)n.put(t%2?17:236,8);return function(t,e,r){let n,i;let l=o.getSymbolTotalCodewords(e),a=l-u.getTotalCodewordsCount(e,r),s=u.getBlocksCount(e,r),c=l%s,f=s-c,h=Math.floor(l/s),p=Math.floor(a/s),g=p+1,m=h-p,b=new d(m),y=0,w=Array(s),v=Array(s),x=0,k=new Uint8Array(t.buffer);for(let t=0;t<s;t++){let e=t<f?p:g;w[t]=k.slice(y,y+e),v[t]=b.encode(w[t]),y+=e,x=Math.max(x,e)}let E=new Uint8Array(l),M=0;for(n=0;n<x;n++)for(i=0;i<s;i++)n<w[i].length&&(E[M++]=w[i][n]);for(n=0;n<m;n++)for(i=0;i<s;i++)E[M++]=v[i][n];return E}(n,t,e)}(e,r,h),w=new l(o.getSymbolSize(e));return function(t,e){let r=t.size,o=s.getPositions(e);for(let e=0;e<o.length;e++){let n=o[e][0],i=o[e][1];for(let e=-1;e<=7;e++)if(!(n+e<=-1)&&!(r<=n+e))for(let o=-1;o<=7;o++)i+o<=-1||r<=i+o||(e>=0&&e<=6&&(0===o||6===o)||o>=0&&o<=6&&(0===e||6===e)||e>=2&&e<=4&&o>=2&&o<=4?t.set(n+e,i+o,!0,!0):t.set(n+e,i+o,!1,!0))}}(w,e),function(t){let e=t.size;for(let r=8;r<e-8;r++){let e=r%2==0;t.set(r,6,e,!0),t.set(6,r,e,!0)}}(w),function(t,e){let r=a.getPositions(e);for(let e=0;e<r.length;e++){let o=r[e][0],n=r[e][1];for(let e=-2;e<=2;e++)for(let r=-2;r<=2;r++)-2===e||2===e||-2===r||2===r||0===e&&0===r?t.set(o+e,n+r,!0,!0):t.set(o+e,n+r,!1,!0)}}(w,e),m(w,r,0),e>=7&&function(t,e){let r,o,n;let i=t.size,l=f.getEncodedBits(e);for(let e=0;e<18;e++)r=Math.floor(e/3),o=e%3+i-8-3,n=(l>>e&1)==1,t.set(r,o,n,!0),t.set(o,r,n,!0)}(w,e),function(t,e){let r=t.size,o=-1,n=r-1,i=7,l=0;for(let a=r-1;a>0;a-=2)for(6===a&&a--;;){for(let r=0;r<2;r++)if(!t.isReserved(n,a-r)){let o=!1;l<e.length&&(o=(e[l]>>>i&1)==1),t.set(n,a-r,o),-1==--i&&(l++,i=7)}if((n+=o)<0||r<=n){n-=o,o=-o;break}}}(w,y),isNaN(n)&&(n=c.getBestMask(w,m.bind(null,w,r))),c.applyMask(n,w),m(w,r,n),{modules:w,version:e,errorCorrectionLevel:r,maskPattern:n,segments:h}}(t,r,b,h)}},4011:function(t,e,r){let o=r(6413);function n(t){this.genPoly=void 0,this.degree=t,this.degree&&this.initialize(this.degree)}n.prototype.initialize=function(t){this.degree=t,this.genPoly=o.generateECPolynomial(this.degree)},n.prototype.encode=function(t){if(!this.genPoly)throw Error("Encoder not initialized");let e=new Uint8Array(t.length+this.degree);e.set(t);let r=o.mod(e,this.genPoly),n=this.degree-r.length;if(n>0){let t=new Uint8Array(this.degree);return t.set(r,n),t}return r},t.exports=n},581:function(t,e){let r="[0-9]+",o="(?:[u3000-u303F]|[u3040-u309F]|[u30A0-u30FF]|[uFF00-uFFEF]|[u4E00-u9FAF]|[u2605-u2606]|[u2190-u2195]|u203B|[u2010u2015u2018u2019u2025u2026u201Cu201Du2225u2260]|[u0391-u0451]|[u00A7u00A8u00B1u00B4u00D7u00F7])+",n="(?:(?![A-Z0-9 $%*+\\-./:]|"+(o=o.replace(/u/g,"\\u"))+")(?:.|[\r\n]))+";e.KANJI=RegExp(o,"g"),e.BYTE_KANJI=RegExp("[^A-Z0-9 $%*+\\-./:]+","g"),e.BYTE=RegExp(n,"g"),e.NUMERIC=RegExp(r,"g"),e.ALPHANUMERIC=RegExp("[A-Z $%*+\\-./:]+","g");let i=RegExp("^"+o+"$"),l=RegExp("^"+r+"$"),a=RegExp("^[A-Z0-9 $%*+\\-./:]+$");e.testKanji=function(t){return i.test(t)},e.testNumeric=function(t){return l.test(t)},e.testAlphanumeric=function(t){return a.test(t)}},9940:function(t,e,r){let o=r(7956),n=r(1687),i=r(7111),l=r(8153),a=r(8514),s=r(581),c=r(2415),u=r(6946);function d(t){return unescape(encodeURIComponent(t)).length}function f(t,e,r){let o;let n=[];for(;null!==(o=t.exec(r));)n.push({data:o[0],index:o.index,mode:e,length:o[0].length});return n}function h(t){let e,r;let n=f(s.NUMERIC,o.NUMERIC,t),i=f(s.ALPHANUMERIC,o.ALPHANUMERIC,t);return c.isKanjiModeEnabled()?(e=f(s.BYTE,o.BYTE,t),r=f(s.KANJI,o.KANJI,t)):(e=f(s.BYTE_KANJI,o.BYTE,t),r=[]),n.concat(i,e,r).sort(function(t,e){return t.index-e.index}).map(function(t){return{data:t.data,mode:t.mode,length:t.length}})}function p(t,e){switch(e){case o.NUMERIC:return n.getBitsLength(t);case o.ALPHANUMERIC:return i.getBitsLength(t);case o.KANJI:return a.getBitsLength(t);case o.BYTE:return l.getBitsLength(t)}}function g(t,e){let r;let s=o.getBestModeForData(t);if((r=o.from(e,s))!==o.BYTE&&r.bit<s.bit)throw Error('"'+t+'" cannot be encoded with mode '+o.toString(r)+".\n Suggested mode is: "+o.toString(s));switch(r!==o.KANJI||c.isKanjiModeEnabled()||(r=o.BYTE),r){case o.NUMERIC:return new n(t);case o.ALPHANUMERIC:return new i(t);case o.KANJI:return new a(t);case o.BYTE:return new l(t)}}e.fromArray=function(t){return t.reduce(function(t,e){return"string"==typeof e?t.push(g(e,null)):e.data&&t.push(g(e.data,e.mode)),t},[])},e.fromString=function(t,r){let n=function(t,e){let r={},n={start:{}},i=["start"];for(let l=0;l<t.length;l++){let a=t[l],s=[];for(let t=0;t<a.length;t++){let c=a[t],u=""+l+t;s.push(u),r[u]={node:c,lastCount:0},n[u]={};for(let t=0;t<i.length;t++){let l=i[t];r[l]&&r[l].node.mode===c.mode?(n[l][u]=p(r[l].lastCount+c.length,c.mode)-p(r[l].lastCount,c.mode),r[l].lastCount+=c.length):(r[l]&&(r[l].lastCount=c.length),n[l][u]=p(c.length,c.mode)+4+o.getCharCountIndicator(c.mode,e))}}i=s}for(let t=0;t<i.length;t++)n[i[t]].end=0;return{map:n,table:r}}(function(t){let e=[];for(let r=0;r<t.length;r++){let n=t[r];switch(n.mode){case o.NUMERIC:e.push([n,{data:n.data,mode:o.ALPHANUMERIC,length:n.length},{data:n.data,mode:o.BYTE,length:n.length}]);break;case o.ALPHANUMERIC:e.push([n,{data:n.data,mode:o.BYTE,length:n.length}]);break;case o.KANJI:e.push([n,{data:n.data,mode:o.BYTE,length:d(n.data)}]);break;case o.BYTE:e.push([{data:n.data,mode:o.BYTE,length:d(n.data)}])}}return e}(h(t,c.isKanjiModeEnabled())),r),i=u.find_path(n.map,"start","end"),l=[];for(let t=1;t<i.length-1;t++)l.push(n.table[i[t]].node);return e.fromArray(l.reduce(function(t,e){let r=t.length-1>=0?t[t.length-1]:null;return r&&r.mode===e.mode?t[t.length-1].data+=e.data:t.push(e),t},[]))},e.rawSplit=function(t){return e.fromArray(h(t,c.isKanjiModeEnabled()))}},2415:function(t,e){let r;let o=[0,26,44,70,100,134,172,196,242,292,346,404,466,532,581,655,733,815,901,991,1085,1156,1258,1364,1474,1588,1706,1828,1921,2051,2185,2323,2465,2611,2761,2876,3034,3196,3362,3532,3706];e.getSymbolSize=function(t){if(!t)throw Error('"version" cannot be null or undefined');if(t<1||t>40)throw Error('"version" should be in range from 1 to 40');return 4*t+17},e.getSymbolTotalCodewords=function(t){return o[t]},e.getBCHDigit=function(t){let e=0;for(;0!==t;)e++,t>>>=1;return e},e.setToSJISFunction=function(t){if("function"!=typeof t)throw Error('"toSJISFunc" is not a valid function.');r=t},e.isKanjiModeEnabled=function(){return void 0!==r},e.toSJIS=function(t){return r(t)}},6623:function(t,e){e.isValid=function(t){return!isNaN(t)&&t>=1&&t<=40}},3379:function(t,e,r){let o=r(2415),n=r(9386),i=r(9947),l=r(7956),a=r(6623),s=o.getBCHDigit(7973);function c(t,e){return l.getCharCountIndicator(t,e)+4}e.from=function(t,e){return a.isValid(t)?parseInt(t,10):e},e.getCapacity=function(t,e,r){if(!a.isValid(t))throw Error("Invalid QR Code version");void 0===r&&(r=l.BYTE);let i=(o.getSymbolTotalCodewords(t)-n.getTotalCodewordsCount(t,e))*8;if(r===l.MIXED)return i;let s=i-c(r,t);switch(r){case l.NUMERIC:return Math.floor(s/10*3);case l.ALPHANUMERIC:return Math.floor(s/11*2);case l.KANJI:return Math.floor(s/13);case l.BYTE:default:return Math.floor(s/8)}},e.getBestVersionForData=function(t,r){let o;let n=i.from(r,i.M);if(Array.isArray(t)){if(t.length>1)return function(t,r){for(let o=1;o<=40;o++)if(function(t,e){let r=0;return t.forEach(function(t){let o=c(t.mode,e);r+=o+t.getBitsLength()}),r}(t,o)<=e.getCapacity(o,r,l.MIXED))return o}(t,n);if(0===t.length)return 1;o=t[0]}else o=t;return function(t,r,o){for(let n=1;n<=40;n++)if(r<=e.getCapacity(n,o,t))return n}(o.mode,o.getLength(),n)},e.getEncodedBits=function(t){if(!a.isValid(t)||t<7)throw Error("Invalid QR Code version");let e=t<<12;for(;o.getBCHDigit(e)-s>=0;)e^=7973<<o.getBCHDigit(e)-s;return t<<12|e}},962:function(t,e,r){let o=r(7220);e.render=function(t,e,r){var n;let i=r,l=e;void 0!==i||e&&e.getContext||(i=e,e=void 0),e||(l=function(){try{return document.createElement("canvas")}catch(t){throw Error("You need to specify a canvas element")}}()),i=o.getOptions(i);let a=o.getImageWidth(t.modules.size,i),s=l.getContext("2d"),c=s.createImageData(a,a);return o.qrToImageData(c.data,t,i),n=l,s.clearRect(0,0,n.width,n.height),n.style||(n.style={}),n.height=a,n.width=a,n.style.height=a+"px",n.style.width=a+"px",s.putImageData(c,0,0),l},e.renderToDataURL=function(t,r,o){let n=o;void 0!==n||r&&r.getContext||(n=r,r=void 0),n||(n={});let i=e.render(t,r,n),l=n.type||"image/png",a=n.rendererOpts||{};return i.toDataURL(l,a.quality)}},5623:function(t,e,r){let o=r(7220);function n(t,e){let r=t.a/255,o=e+'="'+t.hex+'"';return r<1?o+" "+e+'-opacity="'+r.toFixed(2).slice(1)+'"':o}function i(t,e,r){let o=t+e;return void 0!==r&&(o+=" "+r),o}e.render=function(t,e,r){let l=o.getOptions(e),a=t.modules.size,s=t.modules.data,c=a+2*l.margin,u=l.color.light.a?"<path "+n(l.color.light,"fill")+' d="M0 0h'+c+"v"+c+'H0z"/>':"",d="<path "+n(l.color.dark,"stroke")+' d="'+function(t,e,r){let o="",n=0,l=!1,a=0;for(let s=0;s<t.length;s++){let c=Math.floor(s%e),u=Math.floor(s/e);c||l||(l=!0),t[s]?(a++,s>0&&c>0&&t[s-1]||(o+=l?i("M",c+r,.5+u+r):i("m",n,0),n=0,l=!1),c+1<e&&t[s+1]||(o+=i("h",a),a=0)):n++}return o}(s,a,l.margin)+'"/>',f='<svg xmlns="http://www.w3.org/2000/svg" '+(l.width?'width="'+l.width+'" height="'+l.width+'" ':"")+('viewBox="0 0 '+c)+" "+c+'" shape-rendering="crispEdges">'+u+d+"</svg>\n";return"function"==typeof r&&r(null,f),f}},7220:function(t,e){function r(t){if("number"==typeof t&&(t=t.toString()),"string"!=typeof t)throw Error("Color should be defined as hex string");let e=t.slice().replace("#","").split("");if(e.length<3||5===e.length||e.length>8)throw Error("Invalid hex color: "+t);(3===e.length||4===e.length)&&(e=Array.prototype.concat.apply([],e.map(function(t){return[t,t]}))),6===e.length&&e.push("F","F");let r=parseInt(e.join(""),16);return{r:r>>24&255,g:r>>16&255,b:r>>8&255,a:255&r,hex:"#"+e.slice(0,6).join("")}}e.getOptions=function(t){t||(t={}),t.color||(t.color={});let e=void 0===t.margin||null===t.margin||t.margin<0?4:t.margin,o=t.width&&t.width>=21?t.width:void 0,n=t.scale||4;return{width:o,scale:o?4:n,margin:e,color:{dark:r(t.color.dark||"#000000ff"),light:r(t.color.light||"#ffffffff")},type:t.type,rendererOpts:t.rendererOpts||{}}},e.getScale=function(t,e){return e.width&&e.width>=t+2*e.margin?e.width/(t+2*e.margin):e.scale},e.getImageWidth=function(t,r){let o=e.getScale(t,r);return Math.floor((t+2*r.margin)*o)},e.qrToImageData=function(t,r,o){let n=r.modules.size,i=r.modules.data,l=e.getScale(n,o),a=Math.floor((n+2*o.margin)*l),s=o.margin*l,c=[o.color.light,o.color.dark];for(let e=0;e<a;e++)for(let r=0;r<a;r++){let u=(e*a+r)*4,d=o.color.light;e>=s&&r>=s&&e<a-s&&r<a-s&&(d=c[i[Math.floor((e-s)/l)*n+Math.floor((r-s)/l)]?1:0]),t[u++]=d.r,t[u++]=d.g,t[u++]=d.b,t[u]=d.a}}},1994:function(t,e,r){"use strict";function o(){for(var t,e,r=0,o="",n=arguments.length;r<n;r++)(t=arguments[r])&&(e=function t(e){var r,o,n="";if("string"==typeof e||"number"==typeof e)n+=e;else if("object"==typeof e){if(Array.isArray(e)){var i=e.length;for(r=0;r<i;r++)e[r]&&(o=t(e[r]))&&(n&&(n+=" "),n+=o)}else for(o in e)e[o]&&(n&&(n+=" "),n+=o)}return n}(t))&&(o&&(o+=" "),o+=e);return o}r.d(e,{W:function(){return o}})},3335:function(t,e,r){"use strict";r.d(e,{m6:function(){return $}});let o=t=>{let e=a(t),{conflictingClassGroups:r,conflictingClassGroupModifiers:o}=t;return{getClassGroupId:t=>{let r=t.split("-");return""===r[0]&&1!==r.length&&r.shift(),n(r,e)||l(t)},getConflictingClassGroupIds:(t,e)=>{let n=r[t]||[];return e&&o[t]?[...n,...o[t]]:n}}},n=(t,e)=>{if(0===t.length)return e.classGroupId;let r=t[0],o=e.nextPart.get(r),i=o?n(t.slice(1),o):void 0;if(i)return i;if(0===e.validators.length)return;let l=t.join("-");return e.validators.find(({validator:t})=>t(l))?.classGroupId},i=/^\[(.+)\]$/,l=t=>{if(i.test(t)){let e=i.exec(t)[1],r=e?.substring(0,e.indexOf(":"));if(r)return"arbitrary.."+r}},a=t=>{let{theme:e,prefix:r}=t,o={nextPart:new Map,validators:[]};return d(Object.entries(t.classGroups),r).forEach(([t,r])=>{s(r,o,t,e)}),o},s=(t,e,r,o)=>{t.forEach(t=>{if("string"==typeof t){(""===t?e:c(e,t)).classGroupId=r;return}if("function"==typeof t){if(u(t)){s(t(o),e,r,o);return}e.validators.push({validator:t,classGroupId:r});return}Object.entries(t).forEach(([t,n])=>{s(n,c(e,t),r,o)})})},c=(t,e)=>{let r=t;return e.split("-").forEach(t=>{r.nextPart.has(t)||r.nextPart.set(t,{nextPart:new Map,validators:[]}),r=r.nextPart.get(t)}),r},u=t=>t.isThemeGetter,d=(t,e)=>e?t.map(([t,r])=>[t,r.map(t=>"string"==typeof t?e+t:"object"==typeof t?Object.fromEntries(Object.entries(t).map(([t,r])=>[e+t,r])):t)]):t,f=t=>{if(t<1)return{get:()=>void 0,set:()=>{}};let e=0,r=new Map,o=new Map,n=(n,i)=>{r.set(n,i),++e>t&&(e=0,o=r,r=new Map)};return{get(t){let e=r.get(t);return void 0!==e?e:void 0!==(e=o.get(t))?(n(t,e),e):void 0},set(t,e){r.has(t)?r.set(t,e):n(t,e)}}},h=t=>{let{separator:e,experimentalParseClassName:r}=t,o=1===e.length,n=e[0],i=e.length,l=t=>{let r;let l=[],a=0,s=0;for(let c=0;c<t.length;c++){let u=t[c];if(0===a){if(u===n&&(o||t.slice(c,c+i)===e)){l.push(t.slice(s,c)),s=c+i;continue}if("/"===u){r=c;continue}}"["===u?a++:"]"===u&&a--}let c=0===l.length?t:t.substring(s),u=c.startsWith("!"),d=u?c.substring(1):c;return{modifiers:l,hasImportantModifier:u,baseClassName:d,maybePostfixModifierPosition:r&&r>s?r-s:void 0}};return r?t=>r({className:t,parseClassName:l}):l},p=t=>{if(t.length<=1)return t;let e=[],r=[];return t.forEach(t=>{"["===t[0]?(e.push(...r.sort(),t),r=[]):r.push(t)}),e.push(...r.sort()),e},g=t=>({cache:f(t.cacheSize),parseClassName:h(t),...o(t)}),m=/\s+/,b=(t,e)=>{let{parseClassName:r,getClassGroupId:o,getConflictingClassGroupIds:n}=e,i=[],l=t.trim().split(m),a="";for(let t=l.length-1;t>=0;t-=1){let e=l[t],{modifiers:s,hasImportantModifier:c,baseClassName:u,maybePostfixModifierPosition:d}=r(e),f=!!d,h=o(f?u.substring(0,d):u);if(!h){if(!f||!(h=o(u))){a=e+(a.length>0?" "+a:a);continue}f=!1}let g=p(s).join(":"),m=c?g+"!":g,b=m+h;if(i.includes(b))continue;i.push(b);let y=n(h,f);for(let t=0;t<y.length;++t){let e=y[t];i.push(m+e)}a=e+(a.length>0?" "+a:a)}return a};function y(){let t,e,r=0,o="";for(;r<arguments.length;)(t=arguments[r++])&&(e=w(t))&&(o&&(o+=" "),o+=e);return o}let w=t=>{let e;if("string"==typeof t)return t;let r="";for(let o=0;o<t.length;o++)t[o]&&(e=w(t[o]))&&(r&&(r+=" "),r+=e);return r},v=t=>{let e=e=>e[t]||[];return e.isThemeGetter=!0,e},x=/^\[(?:([a-z-]+):)?(.+)\]$/i,k=/^\d+\/\d+$/,E=new Set(["px","full","screen"]),M=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,C=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,A=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,N=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,B=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,I=t=>P(t)||E.has(t)||k.test(t),z=t=>V(t,"length",J),P=t=>!!t&&!Number.isNaN(Number(t)),T=t=>V(t,"number",P),R=t=>!!t&&Number.isInteger(Number(t)),L=t=>t.endsWith("%")&&P(t.slice(0,-1)),S=t=>x.test(t),Z=t=>M.test(t),U=new Set(["length","size","percentage"]),j=t=>V(t,U,K),H=t=>V(t,"position",K),q=new Set(["image","url"]),_=t=>V(t,q,O),F=t=>V(t,"",Y),D=()=>!0,V=(t,e,r)=>{let o=x.exec(t);return!!o&&(o[1]?"string"==typeof e?o[1]===e:e.has(o[1]):r(o[2]))},J=t=>C.test(t)&&!A.test(t),K=()=>!1,Y=t=>N.test(t),O=t=>B.test(t),$=function(t,...e){let r,o,n;let i=function(a){return o=(r=g(e.reduce((t,e)=>e(t),t()))).cache.get,n=r.cache.set,i=l,l(a)};function l(t){let e=o(t);if(e)return e;let i=b(t,r);return n(t,i),i}return function(){return i(y.apply(null,arguments))}}(()=>{let t=v("colors"),e=v("spacing"),r=v("blur"),o=v("brightness"),n=v("borderColor"),i=v("borderRadius"),l=v("borderSpacing"),a=v("borderWidth"),s=v("contrast"),c=v("grayscale"),u=v("hueRotate"),d=v("invert"),f=v("gap"),h=v("gradientColorStops"),p=v("gradientColorStopPositions"),g=v("inset"),m=v("margin"),b=v("opacity"),y=v("padding"),w=v("saturate"),x=v("scale"),k=v("sepia"),E=v("skew"),M=v("space"),C=v("translate"),A=()=>["auto","contain","none"],N=()=>["auto","hidden","clip","visible","scroll"],B=()=>["auto",S,e],U=()=>[S,e],q=()=>["",I,z],V=()=>["auto",P,S],J=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],K=()=>["solid","dashed","dotted","double","none"],Y=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],O=()=>["start","end","center","between","around","evenly","stretch"],$=()=>["","0",S],G=()=>["auto","avoid","all","avoid-page","page","left","right","column"],Q=()=>[P,S];return{cacheSize:500,separator:":",theme:{colors:[D],spacing:[I,z],blur:["none","",Z,S],brightness:Q(),borderColor:[t],borderRadius:["none","","full",Z,S],borderSpacing:U(),borderWidth:q(),contrast:Q(),grayscale:$(),hueRotate:Q(),invert:$(),gap:U(),gradientColorStops:[t],gradientColorStopPositions:[L,z],inset:B(),margin:B(),opacity:Q(),padding:U(),saturate:Q(),scale:Q(),sepia:$(),skew:Q(),space:U(),translate:U()},classGroups:{aspect:[{aspect:["auto","square","video",S]}],container:["container"],columns:[{columns:[Z]}],"break-after":[{"break-after":G()}],"break-before":[{"break-before":G()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...J(),S]}],overflow:[{overflow:N()}],"overflow-x":[{"overflow-x":N()}],"overflow-y":[{"overflow-y":N()}],overscroll:[{overscroll:A()}],"overscroll-x":[{"overscroll-x":A()}],"overscroll-y":[{"overscroll-y":A()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[g]}],"inset-x":[{"inset-x":[g]}],"inset-y":[{"inset-y":[g]}],start:[{start:[g]}],end:[{end:[g]}],top:[{top:[g]}],right:[{right:[g]}],bottom:[{bottom:[g]}],left:[{left:[g]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",R,S]}],basis:[{basis:B()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",S]}],grow:[{grow:$()}],shrink:[{shrink:$()}],order:[{order:["first","last","none",R,S]}],"grid-cols":[{"grid-cols":[D]}],"col-start-end":[{col:["auto",{span:["full",R,S]},S]}],"col-start":[{"col-start":V()}],"col-end":[{"col-end":V()}],"grid-rows":[{"grid-rows":[D]}],"row-start-end":[{row:["auto",{span:[R,S]},S]}],"row-start":[{"row-start":V()}],"row-end":[{"row-end":V()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",S]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",S]}],gap:[{gap:[f]}],"gap-x":[{"gap-x":[f]}],"gap-y":[{"gap-y":[f]}],"justify-content":[{justify:["normal",...O()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...O(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...O(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[y]}],px:[{px:[y]}],py:[{py:[y]}],ps:[{ps:[y]}],pe:[{pe:[y]}],pt:[{pt:[y]}],pr:[{pr:[y]}],pb:[{pb:[y]}],pl:[{pl:[y]}],m:[{m:[m]}],mx:[{mx:[m]}],my:[{my:[m]}],ms:[{ms:[m]}],me:[{me:[m]}],mt:[{mt:[m]}],mr:[{mr:[m]}],mb:[{mb:[m]}],ml:[{ml:[m]}],"space-x":[{"space-x":[M]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[M]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",S,e]}],"min-w":[{"min-w":[S,e,"min","max","fit"]}],"max-w":[{"max-w":[S,e,"none","full","min","max","fit","prose",{screen:[Z]},Z]}],h:[{h:[S,e,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[S,e,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[S,e,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[S,e,"auto","min","max","fit"]}],"font-size":[{text:["base",Z,z]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",T]}],"font-family":[{font:[D]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",S]}],"line-clamp":[{"line-clamp":["none",P,T]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",I,S]}],"list-image":[{"list-image":["none",S]}],"list-style-type":[{list:["none","disc","decimal",S]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[t]}],"placeholder-opacity":[{"placeholder-opacity":[b]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[t]}],"text-opacity":[{"text-opacity":[b]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...K(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",I,z]}],"underline-offset":[{"underline-offset":["auto",I,S]}],"text-decoration-color":[{decoration:[t]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:U()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",S]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",S]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[b]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...J(),H]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",j]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},_]}],"bg-color":[{bg:[t]}],"gradient-from-pos":[{from:[p]}],"gradient-via-pos":[{via:[p]}],"gradient-to-pos":[{to:[p]}],"gradient-from":[{from:[h]}],"gradient-via":[{via:[h]}],"gradient-to":[{to:[h]}],rounded:[{rounded:[i]}],"rounded-s":[{"rounded-s":[i]}],"rounded-e":[{"rounded-e":[i]}],"rounded-t":[{"rounded-t":[i]}],"rounded-r":[{"rounded-r":[i]}],"rounded-b":[{"rounded-b":[i]}],"rounded-l":[{"rounded-l":[i]}],"rounded-ss":[{"rounded-ss":[i]}],"rounded-se":[{"rounded-se":[i]}],"rounded-ee":[{"rounded-ee":[i]}],"rounded-es":[{"rounded-es":[i]}],"rounded-tl":[{"rounded-tl":[i]}],"rounded-tr":[{"rounded-tr":[i]}],"rounded-br":[{"rounded-br":[i]}],"rounded-bl":[{"rounded-bl":[i]}],"border-w":[{border:[a]}],"border-w-x":[{"border-x":[a]}],"border-w-y":[{"border-y":[a]}],"border-w-s":[{"border-s":[a]}],"border-w-e":[{"border-e":[a]}],"border-w-t":[{"border-t":[a]}],"border-w-r":[{"border-r":[a]}],"border-w-b":[{"border-b":[a]}],"border-w-l":[{"border-l":[a]}],"border-opacity":[{"border-opacity":[b]}],"border-style":[{border:[...K(),"hidden"]}],"divide-x":[{"divide-x":[a]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[a]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[b]}],"divide-style":[{divide:K()}],"border-color":[{border:[n]}],"border-color-x":[{"border-x":[n]}],"border-color-y":[{"border-y":[n]}],"border-color-s":[{"border-s":[n]}],"border-color-e":[{"border-e":[n]}],"border-color-t":[{"border-t":[n]}],"border-color-r":[{"border-r":[n]}],"border-color-b":[{"border-b":[n]}],"border-color-l":[{"border-l":[n]}],"divide-color":[{divide:[n]}],"outline-style":[{outline:["",...K()]}],"outline-offset":[{"outline-offset":[I,S]}],"outline-w":[{outline:[I,z]}],"outline-color":[{outline:[t]}],"ring-w":[{ring:q()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[t]}],"ring-opacity":[{"ring-opacity":[b]}],"ring-offset-w":[{"ring-offset":[I,z]}],"ring-offset-color":[{"ring-offset":[t]}],shadow:[{shadow:["","inner","none",Z,F]}],"shadow-color":[{shadow:[D]}],opacity:[{opacity:[b]}],"mix-blend":[{"mix-blend":[...Y(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":Y()}],filter:[{filter:["","none"]}],blur:[{blur:[r]}],brightness:[{brightness:[o]}],contrast:[{contrast:[s]}],"drop-shadow":[{"drop-shadow":["","none",Z,S]}],grayscale:[{grayscale:[c]}],"hue-rotate":[{"hue-rotate":[u]}],invert:[{invert:[d]}],saturate:[{saturate:[w]}],sepia:[{sepia:[k]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[r]}],"backdrop-brightness":[{"backdrop-brightness":[o]}],"backdrop-contrast":[{"backdrop-contrast":[s]}],"backdrop-grayscale":[{"backdrop-grayscale":[c]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[u]}],"backdrop-invert":[{"backdrop-invert":[d]}],"backdrop-opacity":[{"backdrop-opacity":[b]}],"backdrop-saturate":[{"backdrop-saturate":[w]}],"backdrop-sepia":[{"backdrop-sepia":[k]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[l]}],"border-spacing-x":[{"border-spacing-x":[l]}],"border-spacing-y":[{"border-spacing-y":[l]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",S]}],duration:[{duration:Q()}],ease:[{ease:["linear","in","out","in-out",S]}],delay:[{delay:Q()}],animate:[{animate:["none","spin","ping","pulse","bounce",S]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[x]}],"scale-x":[{"scale-x":[x]}],"scale-y":[{"scale-y":[x]}],rotate:[{rotate:[R,S]}],"translate-x":[{"translate-x":[C]}],"translate-y":[{"translate-y":[C]}],"skew-x":[{"skew-x":[E]}],"skew-y":[{"skew-y":[E]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",S]}],accent:[{accent:["auto",t]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",S]}],"caret-color":[{caret:[t]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":U()}],"scroll-mx":[{"scroll-mx":U()}],"scroll-my":[{"scroll-my":U()}],"scroll-ms":[{"scroll-ms":U()}],"scroll-me":[{"scroll-me":U()}],"scroll-mt":[{"scroll-mt":U()}],"scroll-mr":[{"scroll-mr":U()}],"scroll-mb":[{"scroll-mb":U()}],"scroll-ml":[{"scroll-ml":U()}],"scroll-p":[{"scroll-p":U()}],"scroll-px":[{"scroll-px":U()}],"scroll-py":[{"scroll-py":U()}],"scroll-ps":[{"scroll-ps":U()}],"scroll-pe":[{"scroll-pe":U()}],"scroll-pt":[{"scroll-pt":U()}],"scroll-pr":[{"scroll-pr":U()}],"scroll-pb":[{"scroll-pb":U()}],"scroll-pl":[{"scroll-pl":U()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",S]}],fill:[{fill:[t,"none"]}],"stroke-w":[{stroke:[I,z,T]}],stroke:[{stroke:[t,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}})}}]);