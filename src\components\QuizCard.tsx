'use client';

import { useState, useEffect } from 'react';
import { Brain, CheckCircle, XCircle, Clock, Trophy, AlertTriangle } from 'lucide-react';
import { Question, Answer, AIConfig, QuizResult } from '@/types';
import { getQuestion, submitAnswer, getQuizResult, formatQuestionForAI, parseAIAnswer } from '@/lib/quiz';
import { createAIInstance } from '@/lib/ai';
import { cn } from '@/lib/utils';

interface QuizCardProps {
  aiConfig: AIConfig;
  onQuizComplete: (result: QuizResult) => void;
  className?: string;
}

type QuizStatus = 'idle' | 'loading' | 'answering' | 'submitting' | 'completed' | 'error';

export default function QuizCard({ aiConfig, onQuizComplete, className }: QuizCardProps) {
  const [status, setStatus] = useState<QuizStatus>('idle');
  const [currentQuestion, setCurrentQuestion] = useState<Question | null>(null);
  const [questionNum, setQuestionNum] = useState(0);
  const [currentScore, setCurrentScore] = useState(0);
  const [aiAnswer, setAiAnswer] = useState<string>('');
  const [selectedAnswer, setSelectedAnswer] = useState<Answer | null>(null);
  const [error, setError] = useState<string>('');
  const [isAutoMode, setIsAutoMode] = useState(true);

  // 获取下一题
  const fetchNextQuestion = async () => {
    try {
      setStatus('loading');
      setError('');
      setAiAnswer('');
      setSelectedAnswer(null);

      const response = await getQuestion();
      
      if (response.code !== 0) {
        if (response.code === 41103) {
          setError('答题已结束或您已经是硬核会员');
          setStatus('completed');
          return;
        }
        throw new Error(response.message || '获取题目失败');
      }

      if (!response.data) {
        throw new Error('题目数据为空');
      }

      setCurrentQuestion(response.data);
      setQuestionNum(response.data.question_num);
      setStatus('answering');

      // 如果是自动模式，调用AI回答
      if (isAutoMode) {
        await getAIAnswer(response.data);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : '获取题目失败');
      setStatus('error');
    }
  };

  // 获取AI答案
  const getAIAnswer = async (question: Question) => {
    try {
      const aiInstance = createAIInstance(aiConfig);
      const questionText = formatQuestionForAI(question);
      
      const response = await aiInstance.ask(questionText);
      setAiAnswer(response);

      // 解析AI回答
      const answerIndex = parseAIAnswer(response, question.answers.length);
      if (answerIndex && answerIndex >= 1 && answerIndex <= question.answers.length) {
        const answer = question.answers[answerIndex - 1];
        setSelectedAnswer(answer);
        
        // 自动提交答案
        if (isAutoMode) {
          setTimeout(() => {
            submitSelectedAnswer(question.id, answer);
          }, 1000); // 延迟1秒提交，让用户看到AI的选择
        }
      } else {
        setError(`AI回答无效: ${response}`);
      }
    } catch (err) {
      setError(`AI回答失败: ${err instanceof Error ? err.message : '未知错误'}`);
    }
  };

  // 提交选中的答案
  const submitSelectedAnswer = async (questionId: string, answer: Answer) => {
    try {
      setStatus('submitting');
      
      const response = await submitAnswer(questionId, answer.ans_hash, answer.ans_text);
      
      if (response.code === 0) {
        // 获取最新分数
        const result = await getQuizResult();
        const newScore = result.score;
        
        if (newScore > currentScore) {
          setCurrentScore(newScore);
          // 答对了，继续下一题
          setTimeout(fetchNextQuestion, 2000);
        } else {
          // 答错了，也继续下一题
          setTimeout(fetchNextQuestion, 2000);
        }
      } else if (response.code === 41103) {
        // 答题结束
        setStatus('completed');
        const finalResult = await getQuizResult();
        onQuizComplete(finalResult);
      } else {
        throw new Error(response.message || '提交答案失败');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : '提交答案失败');
      setStatus('error');
    }
  };

  // 手动选择答案
  const handleManualAnswer = (answer: Answer) => {
    if (!currentQuestion || status === 'submitting') return;
    
    setSelectedAnswer(answer);
    if (!isAutoMode) {
      submitSelectedAnswer(currentQuestion.id, answer);
    }
  };

  // 开始答题
  const startQuiz = () => {
    setQuestionNum(0);
    setCurrentScore(0);
    fetchNextQuestion();
  };

  // 重试
  const handleRetry = () => {
    setError('');
    if (currentQuestion) {
      if (isAutoMode) {
        getAIAnswer(currentQuestion);
      } else {
        setStatus('answering');
      }
    } else {
      fetchNextQuestion();
    }
  };

  // 组件挂载时开始答题
  useEffect(() => {
    startQuiz();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const getStatusText = () => {
    switch (status) {
      case 'loading':
        return '正在获取题目...';
      case 'answering':
        return isAutoMode ? 'AI正在思考...' : '请选择答案';
      case 'submitting':
        return '正在提交答案...';
      case 'completed':
        return '答题已完成';
      case 'error':
        return '发生错误';
      default:
        return '';
    }
  };

  const getStatusIcon = () => {
    switch (status) {
      case 'loading':
      case 'submitting':
        return <div className="w-5 h-5 loading-spinner" />;
      case 'answering':
        return <Brain className="w-5 h-5 text-bili-blue" />;
      case 'completed':
        return <Trophy className="w-5 h-5 text-yellow-500" />;
      case 'error':
        return <AlertTriangle className="w-5 h-5 text-red-500" />;
      default:
        return null;
    }
  };

  return (
    <div className={cn('card max-w-4xl mx-auto', className)}>
      {/* 头部信息 */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-2">
            {getStatusIcon()}
            <h2 className="text-xl font-bold text-gray-900">
              {questionNum > 0 ? `第${questionNum}题` : '答题系统'}
            </h2>
          </div>
          <div className="text-sm text-gray-500">
            当前得分: <span className="font-medium text-bili-pink">{currentScore}</span>
          </div>
        </div>
        
        {/* 模式切换 */}
        <div className="flex items-center gap-2">
          <label className="flex items-center gap-2 text-sm">
            <input
              type="checkbox"
              checked={isAutoMode}
              onChange={(e) => setIsAutoMode(e.target.checked)}
              disabled={status === 'submitting'}
              className="rounded"
            />
            <span>AI自动答题</span>
          </label>
        </div>
      </div>

      {/* 状态信息 */}
      <div className="mb-4 p-3 bg-gray-50 rounded-lg">
        <div className="flex items-center gap-2 text-sm text-gray-600">
          <Clock className="w-4 h-4" />
          <span>{getStatusText()}</span>
        </div>
      </div>

      {/* 题目内容 */}
      {currentQuestion && (
        <div className="mb-6">
          <div className="mb-4">
            <h3 className="text-lg font-medium text-gray-900 mb-2">题目</h3>
            <p className="text-gray-700 leading-relaxed">{currentQuestion.question}</p>
          </div>

          {/* 选项 */}
          <div className="space-y-3">
            <h4 className="text-md font-medium text-gray-900">选项</h4>
            {currentQuestion.answers.map((answer, index) => (
              <button
                key={index}
                onClick={() => handleManualAnswer(answer)}
                disabled={status === 'submitting' || (isAutoMode && status === 'answering')}
                className={cn(
                  'w-full p-4 text-left border rounded-lg transition-colors',
                  'hover:bg-gray-50 disabled:cursor-not-allowed',
                  selectedAnswer?.ans_hash === answer.ans_hash
                    ? 'border-bili-pink bg-bili-pink/5'
                    : 'border-gray-200'
                )}
              >
                <div className="flex items-start gap-3">
                  <span className="flex-shrink-0 w-6 h-6 bg-gray-100 rounded-full flex items-center justify-center text-sm font-medium">
                    {index + 1}
                  </span>
                  <span className="text-gray-700">{answer.ans_text}</span>
                  {selectedAnswer?.ans_hash === answer.ans_hash && (
                    <CheckCircle className="flex-shrink-0 w-5 h-5 text-bili-pink ml-auto" />
                  )}
                </div>
              </button>
            ))}
          </div>

          {/* AI回答信息 */}
          {aiAnswer && (
            <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <Brain className="w-4 h-4 text-blue-600" />
                <span className="text-sm font-medium text-blue-800">AI回答</span>
              </div>
              <p className="text-sm text-blue-700">{aiAnswer}</p>
            </div>
          )}
        </div>
      )}

      {/* 错误信息 */}
      {error && (
        <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
          <div className="flex items-center gap-2 mb-2">
            <XCircle className="w-4 h-4 text-red-500" />
            <span className="text-sm font-medium text-red-800">错误</span>
          </div>
          <p className="text-sm text-red-700">{error}</p>
          <button
            onClick={handleRetry}
            className="mt-2 btn-outline px-3 py-1 text-sm"
          >
            重试
          </button>
        </div>
      )}

      {/* 手动提交按钮 */}
      {!isAutoMode && selectedAnswer && status === 'answering' && (
        <div className="text-center">
          <button
            onClick={() => currentQuestion && submitSelectedAnswer(currentQuestion.id, selectedAnswer)}
            className="btn-primary px-6 py-2"
          >
            提交答案
          </button>
        </div>
      )}
    </div>
  );
}
