import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import './globals.css';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: '哔哩哔哩硬核会员答题助手',
  description: '基于AI的哔哩哔哩硬核会员自动答题工具，支持DeepSeek、Gemini、OpenAI等多种AI模型',
  keywords: ['哔哩哔哩', '硬核会员', '答题', 'AI', 'DeepSeek', 'Gemini', 'OpenAI'],
  authors: [{ name: 'Bili Hardcore Team' }],
  icons: {
    icon: '/favicon.ico',
  },
  openGraph: {
    title: '哔哩哔哩硬核会员答题助手',
    description: '基于AI的哔哩哔哩硬核会员自动答题工具',
    type: 'website',
    locale: 'zh_CN',
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="zh-CN">
      <body className={inter.className}>
        <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-pink-50">
          {children}
        </div>
      </body>
    </html>
  );
}
