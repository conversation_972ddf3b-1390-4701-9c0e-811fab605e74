2:I[9107,[],"ClientPageRoot"]
3:I[9110,["997","static/chunks/997-14d3a267a57ffcf9.js","931","static/chunks/app/page-62485aca6e50a73b.js"],"default",1]
4:I[4707,[],""]
5:I[6423,[],""]
0:["Elm1x2D5Tajk0x-I7EIq2",[[["",{"children":["__PAGE__",{}]},"$undefined","$undefined",true],["",{"children":["__PAGE__",{},[["$L1",["$","$L2",null,{"props":{"params":{},"searchParams":{}},"Component":"$3"}],null],null],null]},[[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/097d5cd6595d3d5f.css","precedence":"next","crossOrigin":"$undefined"}]],["$","html",null,{"lang":"zh-CN","children":["$","body",null,{"className":"__className_e8ce0c","children":["$","div",null,{"className":"min-h-screen bg-gradient-to-br from-blue-50 via-white to-pink-50","children":["$","$L4",null,{"parallelRouterKey":"children","segmentPath":["children"],"error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L5",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[["$","title",null,{"children":"404: This page could not be found."}],["$","div",null,{"style":{"fontFamily":"system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"","height":"100vh","textAlign":"center","display":"flex","flexDirection":"column","alignItems":"center","justifyContent":"center"},"children":["$","div",null,{"children":[["$","style",null,{"dangerouslySetInnerHTML":{"__html":"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}],["$","h1",null,{"className":"next-error-h1","style":{"display":"inline-block","margin":"0 20px 0 0","padding":"0 23px 0 0","fontSize":24,"fontWeight":500,"verticalAlign":"top","lineHeight":"49px"},"children":"404"}],["$","div",null,{"style":{"display":"inline-block"},"children":["$","h2",null,{"style":{"fontSize":14,"fontWeight":400,"lineHeight":"49px","margin":0},"children":"This page could not be found."}]}]]}]}]],"notFoundStyles":[]}]}]}]}]],null],null],["$L6",null]]]]
6:[["$","meta","0",{"name":"viewport","content":"width=device-width, initial-scale=1"}],["$","meta","1",{"charSet":"utf-8"}],["$","title","2",{"children":"哔哩哔哩硬核会员答题助手"}],["$","meta","3",{"name":"description","content":"基于AI的哔哩哔哩硬核会员自动答题工具，支持DeepSeek、Gemini、OpenAI等多种AI模型"}],["$","meta","4",{"name":"author","content":"Bili Hardcore Team"}],["$","meta","5",{"name":"keywords","content":"哔哩哔哩,硬核会员,答题,AI,DeepSeek,Gemini,OpenAI"}],["$","meta","6",{"property":"og:title","content":"哔哩哔哩硬核会员答题助手"}],["$","meta","7",{"property":"og:description","content":"基于AI的哔哩哔哩硬核会员自动答题工具"}],["$","meta","8",{"property":"og:locale","content":"zh_CN"}],["$","meta","9",{"property":"og:type","content":"website"}],["$","meta","10",{"name":"twitter:card","content":"summary"}],["$","meta","11",{"name":"twitter:title","content":"哔哩哔哩硬核会员答题助手"}],["$","meta","12",{"name":"twitter:description","content":"基于AI的哔哩哔哩硬核会员自动答题工具"}],["$","link","13",{"rel":"icon","href":"/favicon.ico"}]]
1:null
