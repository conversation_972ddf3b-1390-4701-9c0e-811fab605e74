<!DOCTYPE html><html lang="zh-CN"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="/_next/static/css/097d5cd6595d3d5f.css" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-357bf293babb87c5.js"/><script src="/_next/static/chunks/fd9d1056-cf94e0e937e29f3a.js" async=""></script><script src="/_next/static/chunks/117-6cbc83699811aadb.js" async=""></script><script src="/_next/static/chunks/main-app-e5503c88dc7f8b58.js" async=""></script><script src="/_next/static/chunks/997-14d3a267a57ffcf9.js" async=""></script><script src="/_next/static/chunks/app/page-62485aca6e50a73b.js" async=""></script><title>哔哩哔哩硬核会员答题助手</title><meta name="description" content="基于AI的哔哩哔哩硬核会员自动答题工具，支持DeepSeek、Gemini、OpenAI等多种AI模型"/><meta name="author" content="Bili Hardcore Team"/><meta name="keywords" content="哔哩哔哩,硬核会员,答题,AI,DeepSeek,Gemini,OpenAI"/><meta property="og:title" content="哔哩哔哩硬核会员答题助手"/><meta property="og:description" content="基于AI的哔哩哔哩硬核会员自动答题工具"/><meta property="og:locale" content="zh_CN"/><meta property="og:type" content="website"/><meta name="twitter:card" content="summary"/><meta name="twitter:title" content="哔哩哔哩硬核会员答题助手"/><meta name="twitter:description" content="基于AI的哔哩哔哩硬核会员自动答题工具"/><link rel="icon" href="/favicon.ico"/><script src="/_next/static/chunks/polyfills-42372ed130431b0a.js" noModule=""></script></head><body class="__className_e8ce0c"><div class="min-h-screen bg-gradient-to-br from-blue-50 via-white to-pink-50"><div class="min-h-screen flex items-center justify-center"><div class="flex items-center justify-center"><div class="text-center"><div class="loading-spinner mx-auto w-8 h-8"></div><p class="mt-2 text-sm text-gray-600">正在初始化...</p></div></div></div></div><script src="/_next/static/chunks/webpack-357bf293babb87c5.js" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0]);self.__next_f.push([2,null])</script><script>self.__next_f.push([1,"1:HL[\"/_next/static/css/097d5cd6595d3d5f.css\",\"style\"]\n"])</script><script>self.__next_f.push([1,"2:I[2846,[],\"\"]\n4:I[9107,[],\"ClientPageRoot\"]\n5:I[9110,[\"997\",\"static/chunks/997-14d3a267a57ffcf9.js\",\"931\",\"static/chunks/app/page-62485aca6e50a73b.js\"],\"default\",1]\n6:I[4707,[],\"\"]\n7:I[6423,[],\"\"]\n9:I[1060,[],\"\"]\na:[]\n"])</script><script>self.__next_f.push([1,"0:[\"$\",\"$L2\",null,{\"buildId\":\"Elm1x2D5Tajk0x-I7EIq2\",\"assetPrefix\":\"\",\"urlParts\":[\"\",\"\"],\"initialTree\":[\"\",{\"children\":[\"__PAGE__\",{}]},\"$undefined\",\"$undefined\",true],\"initialSeedData\":[\"\",{\"children\":[\"__PAGE__\",{},[[\"$L3\",[\"$\",\"$L4\",null,{\"props\":{\"params\":{},\"searchParams\":{}},\"Component\":\"$5\"}],null],null],null]},[[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/097d5cd6595d3d5f.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\"}]],[\"$\",\"html\",null,{\"lang\":\"zh-CN\",\"children\":[\"$\",\"body\",null,{\"className\":\"__className_e8ce0c\",\"children\":[\"$\",\"div\",null,{\"className\":\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-pink-50\",\"children\":[\"$\",\"$L6\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\"],\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L7\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":\"404\"}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],\"notFoundStyles\":[]}]}]}]}]],null],null],\"couldBeIntercepted\":false,\"initialHead\":[null,\"$L8\"],\"globalErrorComponent\":\"$9\",\"missingSlots\":\"$Wa\"}]\n"])</script><script>self.__next_f.push([1,"8:[[\"$\",\"meta\",\"0\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}],[\"$\",\"meta\",\"1\",{\"charSet\":\"utf-8\"}],[\"$\",\"title\",\"2\",{\"children\":\"哔哩哔哩硬核会员答题助手\"}],[\"$\",\"meta\",\"3\",{\"name\":\"description\",\"content\":\"基于AI的哔哩哔哩硬核会员自动答题工具，支持DeepSeek、Gemini、OpenAI等多种AI模型\"}],[\"$\",\"meta\",\"4\",{\"name\":\"author\",\"content\":\"Bili Hardcore Team\"}],[\"$\",\"meta\",\"5\",{\"name\":\"keywords\",\"content\":\"哔哩哔哩,硬核会员,答题,AI,DeepSeek,Gemini,OpenAI\"}],[\"$\",\"meta\",\"6\",{\"property\":\"og:title\",\"content\":\"哔哩哔哩硬核会员答题助手\"}],[\"$\",\"meta\",\"7\",{\"property\":\"og:description\",\"content\":\"基于AI的哔哩哔哩硬核会员自动答题工具\"}],[\"$\",\"meta\",\"8\",{\"property\":\"og:locale\",\"content\":\"zh_CN\"}],[\"$\",\"meta\",\"9\",{\"property\":\"og:type\",\"content\":\"website\"}],[\"$\",\"meta\",\"10\",{\"name\":\"twitter:card\",\"content\":\"summary\"}],[\"$\",\"meta\",\"11\",{\"name\":\"twitter:title\",\"content\":\"哔哩哔哩硬核会员答题助手\"}],[\"$\",\"meta\",\"12\",{\"name\":\"twitter:description\",\"content\":\"基于AI的哔哩哔哩硬核会员自动答题工具\"}],[\"$\",\"link\",\"13\",{\"rel\":\"icon\",\"href\":\"/favicon.ico\"}]]\n3:null\n"])</script></body></html>