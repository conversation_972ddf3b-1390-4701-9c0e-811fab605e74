'use client';

import { useState, useEffect } from 'react';
import { QrC<PERSON>, RefreshCw, CheckCircle, AlertCircle } from 'lucide-react';
import { getQRCode, pollQRCode, saveAuthData } from '@/lib/auth';
import { generateQRCodeDataURL } from '@/lib/qrcode';
import { AuthData, QRCodeData } from '@/types';
import { cn } from '@/lib/utils';

interface LoginCardProps {
  onLoginSuccess: (authData: AuthData) => void;
  className?: string;
}

type LoginStatus = 'idle' | 'loading' | 'waiting' | 'scanned' | 'success' | 'error' | 'expired';

export default function LoginCard({ onLoginSuccess, className }: LoginCardProps) {
  const [status, setStatus] = useState<LoginStatus>('idle');
  const [qrData, setQrData] = useState<QRCodeData | null>(null);
  const [qrCodeImage, setQrCodeImage] = useState<string>('');
  const [error, setError] = useState<string>('');
  const [retryCount, setRetryCount] = useState(0);

  // 生成二维码
  const generateQRCode = async () => {
    try {
      setStatus('loading');
      setError('');
      
      const data = await getQRCode();
      setQrData(data);
      
      const imageUrl = await generateQRCodeDataURL(data.url);
      setQrCodeImage(imageUrl);
      
      setStatus('waiting');
      startPolling(data.auth_code);
    } catch (err) {
      setError('获取二维码失败，请重试');
      setStatus('error');
    }
  };

  // 开始轮询二维码状态
  const startPolling = (authCode: string) => {
    let pollCount = 0;
    const maxPolls = 60; // 最大轮询次数（60秒）

    const pollInterval = setInterval(async () => {
      try {
        if (pollCount >= maxPolls) {
          clearInterval(pollInterval);
          setStatus('expired');
          return;
        }

        const result = await pollQRCode(authCode);
        
        if (result.code === 0 && result.data) {
          // 登录成功
          clearInterval(pollInterval);
          setStatus('success');
          
          // 构建认证数据
          const cookies = result.data.cookie_info.cookies;
          let csrf = '';
          
          for (const cookie of cookies) {
            if (cookie.name === 'bili_jct') {
              csrf = cookie.value;
              break;
            }
          }

          const cookieString = cookies
            .map(cookie => `${cookie.name}=${cookie.value}`)
            .join(';');

          const authData: AuthData = {
            access_token: result.data.access_token,
            csrf,
            mid: String(result.data.mid),
            cookie: cookieString,
          };

          saveAuthData(authData);
          onLoginSuccess(authData);
        } else if (result.code === 86101) {
          // 二维码未扫描，继续轮询
          pollCount++;
        } else if (result.code === 86090) {
          // 二维码已扫描但未确认
          setStatus('scanned');
          pollCount++;
        } else if (result.code === 86038) {
          // 二维码已过期
          clearInterval(pollInterval);
          setStatus('expired');
        } else {
          // 其他错误
          clearInterval(pollInterval);
          setError(result.message || '登录失败');
          setStatus('error');
        }
      } catch (err) {
        pollCount++;
        if (pollCount >= maxPolls) {
          clearInterval(pollInterval);
          setError('网络连接超时，请重试');
          setStatus('error');
        }
      }
    }, 1000);
  };

  // 重新生成二维码
  const handleRefresh = () => {
    setRetryCount(prev => prev + 1);
    generateQRCode();
  };

  // 组件挂载时生成二维码
  useEffect(() => {
    generateQRCode();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const getStatusText = () => {
    switch (status) {
      case 'loading':
        return '正在生成二维码...';
      case 'waiting':
        return '请使用哔哩哔哩APP扫描二维码登录';
      case 'scanned':
        return '扫描成功，请在手机上确认登录';
      case 'success':
        return '登录成功！';
      case 'expired':
        return '二维码已过期，请点击刷新';
      case 'error':
        return error || '发生错误，请重试';
      default:
        return '';
    }
  };

  const getStatusIcon = () => {
    switch (status) {
      case 'loading':
        return <div className="w-5 h-5 loading-spinner" />;
      case 'waiting':
        return <QrCode className="w-5 h-5 text-bili-blue" />;
      case 'scanned':
        return <CheckCircle className="w-5 h-5 text-yellow-500" />;
      case 'success':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'expired':
      case 'error':
        return <AlertCircle className="w-5 h-5 text-red-500" />;
      default:
        return null;
    }
  };

  return (
    <div className={cn('card max-w-md mx-auto', className)}>
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">
          哔哩哔哩登录
        </h2>
        
        {/* 二维码区域 */}
        <div className="mb-6">
          <div className="relative inline-block">
            {qrCodeImage && status !== 'loading' ? (
              <img
                src={qrCodeImage}
                alt="登录二维码"
                className={cn(
                  'w-64 h-64 border-2 border-gray-200 rounded-lg',
                  status === 'expired' && 'opacity-50 grayscale'
                )}
              />
            ) : (
              <div className="w-64 h-64 border-2 border-gray-200 rounded-lg flex items-center justify-center bg-gray-50">
                <div className="w-8 h-8 loading-spinner" />
              </div>
            )}
            
            {/* 刷新按钮 */}
            {(status === 'expired' || status === 'error') && (
              <button
                onClick={handleRefresh}
                className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50 rounded-lg text-white hover:bg-opacity-60 transition-colors"
              >
                <RefreshCw className="w-8 h-8" />
              </button>
            )}
          </div>
        </div>

        {/* 状态信息 */}
        <div className="flex items-center justify-center gap-2 mb-4">
          {getStatusIcon()}
          <span className={cn(
            'text-sm',
            status === 'error' || status === 'expired' ? 'text-red-600' :
            status === 'success' ? 'text-green-600' :
            status === 'scanned' ? 'text-yellow-600' :
            'text-gray-600'
          )}>
            {getStatusText()}
          </span>
        </div>

        {/* 提示信息 */}
        <div className="text-xs text-gray-500 space-y-1">
          <p>1. 打开哔哩哔哩APP</p>
          <p>2. 点击右下角"我的"</p>
          <p>3. 点击右上角扫码图标</p>
          <p>4. 扫描上方二维码完成登录</p>
        </div>

        {/* 手动刷新按钮 */}
        {status !== 'loading' && status !== 'success' && (
          <button
            onClick={handleRefresh}
            className="mt-4 btn-outline px-4 py-2 text-sm"
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            刷新二维码
          </button>
        )}
      </div>
    </div>
  );
}
