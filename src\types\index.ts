// 用户认证相关类型
export interface AuthData {
  access_token: string;
  csrf: string;
  mid: string;
  cookie: string;
}

export interface QRCodeData {
  url: string;
  auth_code: string;
}

export interface QRCodePollResponse {
  code: number;
  message?: string;
  data?: {
    access_token: string;
    mid: number;
    cookie_info: {
      cookies: Array<{
        name: string;
        value: string;
      }>;
    };
  };
}

// AI配置相关类型
export interface AIConfig {
  model_choice: '1' | '2' | '3'; // 1: DeepSeek, 2: Gemini, 3: OpenAI
  api_key_deepseek?: string;
  api_key_gemini?: string;
  openai_config?: {
    base_url: string;
    model: string;
    api_key: string;
  };
}

// 答题相关类型
export interface Answer {
  ans_text: string;
  ans_hash: string;
}

export interface Question {
  id: string;
  question: string;
  answers: Answer[];
  question_num: number;
  source?: string;
  author?: string;
}

export interface Category {
  id: string;
  name: string;
}

export interface CaptchaData {
  url: string;
  token: string;
}

export interface QuizResult {
  score: number;
  scores: Array<{
    category: string;
    score: number;
    total: number;
  }>;
}

// API响应类型
export interface APIResponse<T = any> {
  code: number;
  message?: string;
  data?: T;
}

// 应用状态类型
export interface AppState {
  isAuthenticated: boolean;
  authData: AuthData | null;
  aiConfig: AIConfig | null;
  currentQuestion: Question | null;
  quizResult: QuizResult | null;
  isLoading: boolean;
  error: string | null;
}

// 本地存储键名
export const STORAGE_KEYS = {
  AUTH_DATA: 'bili-hardcore-auth',
  AI_CONFIG: 'bili-hardcore-ai-config',
  QUIZ_PROGRESS: 'bili-hardcore-quiz-progress',
} as const;
