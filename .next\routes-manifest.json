{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [], "headers": [{"source": "/(.*)", "headers": [{"key": "Cross-Origin-Embedder-Policy", "value": "unsafe-none"}, {"key": "Cross-Origin-Opener-Policy", "value": "same-origin-allow-popups"}], "regex": "^(?:/(.*))(?:/)?$"}], "dynamicRoutes": [], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc"}, "rewrites": []}