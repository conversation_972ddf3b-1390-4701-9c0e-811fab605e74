'use client';

import { useState, useEffect } from 'react';
import { Shield, RefreshCw, CheckCircle, AlertCircle, ExternalLink } from 'lucide-react';
import { Category, CaptchaData } from '@/types';
import { getCategories, getCaptcha, submitCaptcha } from '@/lib/quiz';
import { cn } from '@/lib/utils';

interface CaptchaCardProps {
  onCaptchaComplete: (categoryIds: string) => void;
  className?: string;
}

type CaptchaStatus = 'loading' | 'selecting' | 'captcha' | 'submitting' | 'error';

export default function CaptchaCard({ onCaptchaComplete, className }: CaptchaCardProps) {
  const [status, setStatus] = useState<CaptchaStatus>('loading');
  const [categories, setCategories] = useState<Category[]>([]);
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [captchaData, setCaptchaData] = useState<CaptchaData | null>(null);
  const [captchaCode, setCaptchaCode] = useState('');
  const [error, setError] = useState<string>('');

  // 获取分类信息
  const fetchCategories = async () => {
    try {
      setStatus('loading');
      setError('');
      
      const categoryList = await getCategories();
      setCategories(categoryList);
      setStatus('selecting');
    } catch (err) {
      setError(err instanceof Error ? err.message : '获取分类失败');
      setStatus('error');
    }
  };

  // 获取验证码
  const fetchCaptcha = async () => {
    try {
      setStatus('loading');
      setError('');
      
      const data = await getCaptcha();
      setCaptchaData(data);
      setStatus('captcha');
    } catch (err) {
      setError(err instanceof Error ? err.message : '获取验证码失败');
      setStatus('error');
    }
  };

  // 处理分类选择
  const handleCategoryToggle = (categoryId: string) => {
    setSelectedCategories(prev => {
      const isSelected = prev.includes(categoryId);
      if (isSelected) {
        return prev.filter(id => id !== categoryId);
      } else {
        // 最多选择3个分类
        if (prev.length >= 3) {
          return prev;
        }
        return [...prev, categoryId];
      }
    });
  };

  // 确认分类选择
  const handleCategoryConfirm = () => {
    if (selectedCategories.length === 0) {
      setError('请至少选择一个分类');
      return;
    }
    fetchCaptcha();
  };

  // 提交验证码
  const handleCaptchaSubmit = async () => {
    if (!captchaCode.trim()) {
      setError('请输入验证码');
      return;
    }

    if (!captchaData) {
      setError('验证码数据丢失，请重新获取');
      return;
    }

    try {
      setStatus('submitting');
      setError('');

      const categoryIds = selectedCategories.join(',');
      const success = await submitCaptcha(captchaCode, captchaData.token, categoryIds);
      
      if (success) {
        onCaptchaComplete(categoryIds);
      } else {
        throw new Error('验证码验证失败');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : '验证码提交失败');
      setStatus('captcha');
    }
  };

  // 重新获取验证码
  const handleRefreshCaptcha = () => {
    setCaptchaCode('');
    fetchCaptcha();
  };

  // 重试
  const handleRetry = () => {
    setError('');
    if (status === 'error' && categories.length === 0) {
      fetchCategories();
    } else if (status === 'error' && !captchaData) {
      fetchCaptcha();
    } else {
      setStatus('captcha');
    }
  };

  // 组件挂载时获取分类
  useEffect(() => {
    fetchCategories();
  }, []);

  const getStatusText = () => {
    switch (status) {
      case 'loading':
        return '正在加载...';
      case 'selecting':
        return '请选择答题分类';
      case 'captcha':
        return '请输入验证码';
      case 'submitting':
        return '正在验证...';
      case 'error':
        return '发生错误';
      default:
        return '';
    }
  };

  const getStatusIcon = () => {
    switch (status) {
      case 'loading':
      case 'submitting':
        return <div className="w-5 h-5 loading-spinner" />;
      case 'selecting':
      case 'captcha':
        return <Shield className="w-5 h-5 text-bili-blue" />;
      case 'error':
        return <AlertCircle className="w-5 h-5 text-red-500" />;
      default:
        return null;
    }
  };

  return (
    <div className={cn('card max-w-2xl mx-auto', className)}>
      {/* 头部 */}
      <div className="text-center mb-6">
        <div className="flex items-center justify-center gap-2 mb-2">
          {getStatusIcon()}
          <h2 className="text-2xl font-bold text-gray-900">验证码验证</h2>
        </div>
        <p className="text-gray-600">{getStatusText()}</p>
      </div>

      {/* 分类选择 */}
      {status === 'selecting' && (
        <div className="space-y-4">
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-3">选择答题分类</h3>
            <p className="text-sm text-gray-600 mb-4">
              请选择1-3个分类（最多3个），建议选择知识区和历史区以获得更高正确率
            </p>
            <div className="grid grid-cols-2 gap-3">
              {categories.map((category) => (
                <button
                  key={category.id}
                  onClick={() => handleCategoryToggle(category.id)}
                  disabled={!selectedCategories.includes(category.id) && selectedCategories.length >= 3}
                  className={cn(
                    'p-3 text-left border rounded-lg transition-colors',
                    'hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed',
                    selectedCategories.includes(category.id)
                      ? 'border-bili-pink bg-bili-pink/5'
                      : 'border-gray-200'
                  )}
                >
                  <div className="flex items-center gap-2">
                    {selectedCategories.includes(category.id) && (
                      <CheckCircle className="w-4 h-4 text-bili-pink" />
                    )}
                    <span className="text-sm font-medium">{category.name}</span>
                  </div>
                </button>
              ))}
            </div>
          </div>

          <div className="text-center">
            <button
              onClick={handleCategoryConfirm}
              disabled={selectedCategories.length === 0}
              className="btn-primary px-6 py-2 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              确认选择 ({selectedCategories.length}/3)
            </button>
          </div>
        </div>
      )}

      {/* 验证码输入 */}
      {status === 'captcha' && captchaData && (
        <div className="space-y-4">
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-3">输入验证码</h3>
            
            {/* 验证码图片 */}
            <div className="mb-4">
              <div className="flex items-center gap-3 mb-2">
                <span className="text-sm text-gray-600">验证码图片：</span>
                <button
                  onClick={handleRefreshCaptcha}
                  className="btn-outline px-2 py-1 text-xs"
                >
                  <RefreshCw className="w-3 h-3 mr-1" />
                  刷新
                </button>
              </div>
              <a
                href={captchaData.url}
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center gap-2 p-3 bg-gray-50 border border-gray-200 rounded-lg hover:bg-gray-100 transition-colors"
              >
                <ExternalLink className="w-4 h-4 text-bili-blue" />
                <span className="text-sm text-bili-blue">点击查看验证码</span>
              </a>
            </div>

            {/* 验证码输入框 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                验证码
              </label>
              <input
                type="text"
                value={captchaCode}
                onChange={(e) => setCaptchaCode(e.target.value)}
                placeholder="请输入验证码"
                className="input"
                disabled={false}
                onKeyPress={(e) => {
                  if (e.key === 'Enter') {
                    handleCaptchaSubmit();
                  }
                }}
              />
            </div>
          </div>

          <div className="text-center">
            <button
              onClick={handleCaptchaSubmit}
              disabled={!captchaCode.trim()}
              className="btn-primary px-6 py-2 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {false ? (
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 loading-spinner border-white" />
                  <span>验证中...</span>
                </div>
              ) : (
                '提交验证码'
              )}
            </button>
          </div>
        </div>
      )}

      {/* 错误信息 */}
      {error && (
        <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
          <div className="flex items-center gap-2 mb-2">
            <AlertCircle className="w-4 h-4 text-red-500" />
            <span className="text-sm font-medium text-red-800">错误</span>
          </div>
          <p className="text-sm text-red-700 mb-2">{error}</p>
          <button
            onClick={handleRetry}
            className="btn-outline px-3 py-1 text-sm"
          >
            重试
          </button>
        </div>
      )}

      {/* 提示信息 */}
      <div className="mt-6 p-3 bg-blue-50 border border-blue-200 rounded-lg">
        <div className="text-sm text-blue-800">
          <p className="font-medium mb-1">提示：</p>
          <ul className="space-y-1 text-xs">
            <li>• 验证码用于确认您的答题分类选择</li>
            <li>• 建议选择知识区和历史区，正确率更高</li>
            <li>• 每日最多可以答题3次</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
