/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=E%3A%5CTemp%5Cbili-hardcore%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5CTemp%5Cbili-hardcore&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=E%3A%5CTemp%5Cbili-hardcore%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5CTemp%5Cbili-hardcore&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\")), \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=E%3A%5CTemp%5Cbili-hardcore%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5CTemp%5Cbili-hardcore&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CTemp%5C%5Cbili-hardcore%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTemp%5C%5Cbili-hardcore%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTemp%5C%5Cbili-hardcore%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTemp%5C%5Cbili-hardcore%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTemp%5C%5Cbili-hardcore%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTemp%5C%5Cbili-hardcore%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CTemp%5C%5Cbili-hardcore%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTemp%5C%5Cbili-hardcore%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTemp%5C%5Cbili-hardcore%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTemp%5C%5Cbili-hardcore%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTemp%5C%5Cbili-hardcore%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTemp%5C%5Cbili-hardcore%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CTemp%5C%5Cbili-hardcore%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTemp%5C%5Cbili-hardcore%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTemp%5C%5Cbili-hardcore%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTemp%5C%5Cbili-hardcore%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTemp%5C%5Cbili-hardcore%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTemp%5C%5Cbili-hardcore%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CTemp%5C%5Cbili-hardcore%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTemp%5C%5Cbili-hardcore%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CTemp%5C%5Cbili-hardcore%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTemp%5C%5Cbili-hardcore%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CTemp%5C%5Cbili-hardcore%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CTemp%5C%5Cbili-hardcore%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUNUZW1wJTVDJTVDYmlsaS1oYXJkY29yZSU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnSkFBZ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9iaWxpLWhhcmRjb3JlLXdlYi8/NTkwMCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkU6XFxcXFRlbXBcXFxcYmlsaS1oYXJkY29yZVxcXFxzcmNcXFxcYXBwXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CTemp%5C%5Cbili-hardcore%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Github_Heart_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Github,Heart,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Github_Heart_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Github,Heart,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/github.js\");\n/* harmony import */ var _barrel_optimize_names_Github_Heart_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Github,Heart,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _components_LoginCard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/LoginCard */ \"(ssr)/./src/components/LoginCard.tsx\");\n/* harmony import */ var _components_AIConfigCard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/AIConfigCard */ \"(ssr)/./src/components/AIConfigCard.tsx\");\n/* harmony import */ var _components_CaptchaCard__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/CaptchaCard */ \"(ssr)/./src/components/CaptchaCard.tsx\");\n/* harmony import */ var _components_QuizCard__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/QuizCard */ \"(ssr)/./src/components/QuizCard.tsx\");\n/* harmony import */ var _components_ResultCard__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ResultCard */ \"(ssr)/./src/components/ResultCard.tsx\");\n/* harmony import */ var _components_ErrorBoundary__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ErrorBoundary */ \"(ssr)/./src/components/ErrorBoundary.tsx\");\n/* harmony import */ var _components_LoadingSpinner__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/LoadingSpinner */ \"(ssr)/./src/components/LoadingSpinner.tsx\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/auth */ \"(ssr)/./src/lib/auth.ts\");\n/* harmony import */ var _lib_ai__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/ai */ \"(ssr)/./src/lib/ai.ts\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./src/lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\n\n\n\nfunction HomePage() {\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"login\");\n    const [authData, setAuthDataState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [aiConfig, setAIConfigState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [quizResult, setQuizResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // 初始化应用状态\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const initializeApp = async ()=>{\n            try {\n                // 检查认证状态\n                const savedAuthData = (0,_lib_auth__WEBPACK_IMPORTED_MODULE_9__.loadAuthData)();\n                if (savedAuthData) {\n                    const isValid = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_9__.validateAuth)();\n                    if (isValid) {\n                        setAuthDataState(savedAuthData);\n                        (0,_lib_api__WEBPACK_IMPORTED_MODULE_11__.setAuthData)(savedAuthData);\n                        // 检查AI配置\n                        const savedAIConfig = (0,_lib_ai__WEBPACK_IMPORTED_MODULE_10__.loadAIConfig)();\n                        if (savedAIConfig) {\n                            setAIConfigState(savedAIConfig);\n                            setCurrentStep(\"quiz\");\n                        } else {\n                            setCurrentStep(\"ai-config\");\n                        }\n                    } else {\n                        setCurrentStep(\"login\");\n                    }\n                } else {\n                    setCurrentStep(\"login\");\n                }\n            } catch (error) {\n                console.error(\"初始化应用失败:\", error);\n                setCurrentStep(\"login\");\n            } finally{\n                setIsLoading(false);\n            }\n        };\n        initializeApp();\n    }, []);\n    // 处理登录成功\n    const handleLoginSuccess = (data)=>{\n        setAuthDataState(data);\n        (0,_lib_api__WEBPACK_IMPORTED_MODULE_11__.setAuthData)(data);\n        setCurrentStep(\"ai-config\");\n    };\n    // 处理AI配置完成\n    const handleAIConfigComplete = (config)=>{\n        setAIConfigState(config);\n        setCurrentStep(\"quiz\");\n    };\n    // 处理验证码完成\n    const handleCaptchaComplete = (categoryIds)=>{\n        // 验证码完成后继续答题\n        setCurrentStep(\"quiz\");\n    };\n    // 处理答题完成\n    const handleQuizComplete = (result)=>{\n        setQuizResult(result);\n        setCurrentStep(\"result\");\n    };\n    // 重新开始\n    const handleRestart = ()=>{\n        setQuizResult(null);\n        setCurrentStep(\"quiz\");\n    };\n    // 如果正在加载，显示加载界面\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LoadingSpinner__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                size: \"lg\",\n                text: \"正在初始化...\"\n            }, void 0, false, {\n                fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 98,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 97,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ErrorBoundary__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                    className: \"bg-white/80 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-40\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between h-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 bg-bili-pink rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Github_Heart_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"w-5 h-5 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 112,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 111,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-lg font-bold text-gray-900\",\n                                                    children: \"哔哩哔哩硬核会员答题助手\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 115,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-500\",\n                                                    children: \"基于AI的自动答题工具\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 118,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 114,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"https://github.com/Karben233/bili-hardcore\",\n                                        target: \"_blank\",\n                                        rel: \"noopener noreferrer\",\n                                        className: \"flex items-center gap-2 text-sm text-gray-600 hover:text-gray-900 transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Github_Heart_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 131,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hidden sm:inline\",\n                                                children: \"GitHub\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 132,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 125,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 124,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 107,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        {\n                                            key: \"login\",\n                                            label: \"登录\",\n                                            step: 1\n                                        },\n                                        {\n                                            key: \"ai-config\",\n                                            label: \"AI配置\",\n                                            step: 2\n                                        },\n                                        {\n                                            key: \"quiz\",\n                                            label: \"答题\",\n                                            step: 3\n                                        },\n                                        {\n                                            key: \"result\",\n                                            label: \"结果\",\n                                            step: 4\n                                        }\n                                    ].map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: `w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${currentStep === item.key ? \"bg-bili-pink text-white\" : [\n                                                        \"login\",\n                                                        \"ai-config\",\n                                                        \"quiz\"\n                                                    ].indexOf(currentStep) > [\n                                                        \"login\",\n                                                        \"ai-config\",\n                                                        \"quiz\"\n                                                    ].indexOf(item.key) ? \"bg-green-500 text-white\" : \"bg-gray-200 text-gray-600\"}`,\n                                                    children: item.step\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 152,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: `ml-2 text-sm ${currentStep === item.key ? \"text-bili-pink font-medium\" : \"text-gray-500\"}`,\n                                                    children: item.label\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 163,\n                                                    columnNumber: 19\n                                                }, this),\n                                                index < 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-8 h-px bg-gray-300 mx-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 173,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, item.key, true, {\n                                            fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 151,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 142,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-fade-in\",\n                            children: [\n                                currentStep === \"login\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LoginCard__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    onLoginSuccess: handleLoginSuccess\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 184,\n                                    columnNumber: 13\n                                }, this),\n                                currentStep === \"ai-config\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AIConfigCard__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    onConfigComplete: handleAIConfigComplete\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 13\n                                }, this),\n                                currentStep === \"captcha\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CaptchaCard__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    onCaptchaComplete: handleCaptchaComplete\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 192,\n                                    columnNumber: 13\n                                }, this),\n                                currentStep === \"quiz\" && aiConfig && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_QuizCard__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    aiConfig: aiConfig,\n                                    onQuizComplete: handleQuizComplete\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 13\n                                }, this),\n                                currentStep === \"result\" && quizResult && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ResultCard__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    result: quizResult,\n                                    onRestart: handleRestart\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 203,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 182,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 140,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                    className: \"bg-white/80 backdrop-blur-sm border-t border-gray-200 mt-16\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-center gap-2 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Github_Heart_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"w-4 h-4 text-red-500\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 216,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"本软件免费且代码开源\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 217,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 215,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2 text-xs text-gray-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                \"源码地址：\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"https://github.com/Karben233/bili-hardcore\",\n                                                    target: \"_blank\",\n                                                    rel: \"noopener noreferrer\",\n                                                    className: \"text-bili-blue hover:underline ml-1\",\n                                                    children: \"https://github.com/Karben233/bili-hardcore\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 224,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 222,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"问题反馈：请在GitHub仓库中提交Issue\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 233,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-400\",\n                                            children: \"免责声明：本工具仅供学习交流使用，请遵守相关平台的使用条款\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 221,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 214,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 213,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 212,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 105,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 104,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/AIConfigCard.tsx":
/*!*****************************************!*\
  !*** ./src/components/AIConfigCard.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AIConfigCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bot_Eye_EyeOff_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bot,Eye,EyeOff,Settings!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bot_Eye_EyeOff_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bot,Eye,EyeOff,Settings!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bot_Eye_EyeOff_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bot,Eye,EyeOff,Settings!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bot_Eye_EyeOff_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bot,Eye,EyeOff,Settings!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bot_Eye_EyeOff_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bot,Eye,EyeOff,Settings!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _lib_ai__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/ai */ \"(ssr)/./src/lib/ai.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction AIConfigCard({ onConfigComplete, className }) {\n    const [config, setConfig] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        model_choice: \"1\"\n    });\n    const [showKeys, setShowKeys] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        deepseek: false,\n        gemini: false,\n        openai: false\n    });\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 加载已保存的配置\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const savedConfig = (0,_lib_ai__WEBPACK_IMPORTED_MODULE_2__.loadAIConfig)();\n        if (savedConfig) {\n            setConfig(savedConfig);\n        }\n    }, []);\n    // 处理模型选择变化\n    const handleModelChange = (modelChoice)=>{\n        setConfig((prev)=>({\n                ...prev,\n                model_choice: modelChoice\n            }));\n        setErrors({});\n    };\n    // 处理DeepSeek API密钥变化\n    const handleDeepSeekKeyChange = (value)=>{\n        setConfig((prev)=>({\n                ...prev,\n                api_key_deepseek: value\n            }));\n        if (errors.deepseek) {\n            setErrors((prev)=>({\n                    ...prev,\n                    deepseek: \"\"\n                }));\n        }\n    };\n    // 处理Gemini API密钥变化\n    const handleGeminiKeyChange = (value)=>{\n        setConfig((prev)=>({\n                ...prev,\n                api_key_gemini: value\n            }));\n        if (errors.gemini) {\n            setErrors((prev)=>({\n                    ...prev,\n                    gemini: \"\"\n                }));\n        }\n    };\n    // 处理OpenAI配置变化\n    const handleOpenAIConfigChange = (field, value)=>{\n        setConfig((prev)=>({\n                ...prev,\n                openai_config: {\n                    ...prev.openai_config,\n                    [field]: value\n                }\n            }));\n        if (errors.openai) {\n            setErrors((prev)=>({\n                    ...prev,\n                    openai: \"\"\n                }));\n        }\n    };\n    // 验证并保存配置\n    const handleSave = async ()=>{\n        setIsLoading(true);\n        setErrors({});\n        try {\n            const validation = (0,_lib_ai__WEBPACK_IMPORTED_MODULE_2__.validateAIConfig)(config);\n            if (!validation.isValid) {\n                setErrors({\n                    general: validation.error || \"配置验证失败\"\n                });\n                return;\n            }\n            (0,_lib_ai__WEBPACK_IMPORTED_MODULE_2__.saveAIConfig)(config);\n            onConfigComplete(config);\n        } catch (error) {\n            setErrors({\n                general: \"保存配置失败，请重试\"\n            });\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // 切换密钥显示状态\n    const toggleKeyVisibility = (type)=>{\n        setShowKeys((prev)=>({\n                ...prev,\n                [type]: !prev[type]\n            }));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"card max-w-2xl mx-auto\", className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center gap-2 mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bot_Eye_EyeOff_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"w-6 h-6 text-bili-blue\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\AIConfigCard.tsx\",\n                                lineNumber: 112,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: \"AI模型配置\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\AIConfigCard.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\AIConfigCard.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"选择并配置用于自动答题的AI模型\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\AIConfigCard.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\AIConfigCard.tsx\",\n                lineNumber: 110,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                        className: \"block text-sm font-medium text-gray-700 mb-3\",\n                        children: \"选择AI模型\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\AIConfigCard.tsx\",\n                        lineNumber: 120,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"flex items-center p-3 border rounded-lg cursor-pointer hover:bg-gray-50\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"radio\",\n                                        name: \"model\",\n                                        value: \"1\",\n                                        checked: config.model_choice === \"1\",\n                                        onChange: (e)=>handleModelChange(e.target.value),\n                                        className: \"mr-3\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\AIConfigCard.tsx\",\n                                        lineNumber: 126,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"font-medium\",\n                                                children: \"DeepSeek (V3)\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\AIConfigCard.tsx\",\n                                                lineNumber: 135,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: \"推荐选择，性价比高\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\AIConfigCard.tsx\",\n                                                lineNumber: 136,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\AIConfigCard.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\AIConfigCard.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"flex items-center p-3 border rounded-lg cursor-pointer hover:bg-gray-50\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"radio\",\n                                        name: \"model\",\n                                        value: \"2\",\n                                        checked: config.model_choice === \"2\",\n                                        onChange: (e)=>handleModelChange(e.target.value),\n                                        className: \"mr-3\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\AIConfigCard.tsx\",\n                                        lineNumber: 142,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"font-medium\",\n                                                children: \"Gemini (2.0-flash)\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\AIConfigCard.tsx\",\n                                                lineNumber: 151,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: \"免费版可能会触发风控429报错\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\AIConfigCard.tsx\",\n                                                lineNumber: 152,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\AIConfigCard.tsx\",\n                                        lineNumber: 150,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\AIConfigCard.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"flex items-center p-3 border rounded-lg cursor-pointer hover:bg-gray-50\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"radio\",\n                                        name: \"model\",\n                                        value: \"3\",\n                                        checked: config.model_choice === \"3\",\n                                        onChange: (e)=>handleModelChange(e.target.value),\n                                        className: \"mr-3\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\AIConfigCard.tsx\",\n                                        lineNumber: 158,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"font-medium\",\n                                                children: \"自定义OpenAI格式API\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\AIConfigCard.tsx\",\n                                                lineNumber: 167,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: \"支持OpenAI、火山引擎、硅基流动等\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\AIConfigCard.tsx\",\n                                                lineNumber: 168,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\AIConfigCard.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\AIConfigCard.tsx\",\n                                lineNumber: 157,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\AIConfigCard.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\AIConfigCard.tsx\",\n                lineNumber: 119,\n                columnNumber: 7\n            }, this),\n            config.model_choice === \"1\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                        children: \"DeepSeek API密钥\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\AIConfigCard.tsx\",\n                        lineNumber: 177,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: showKeys.deepseek ? \"text\" : \"password\",\n                                value: config.api_key_deepseek || \"\",\n                                onChange: (e)=>handleDeepSeekKeyChange(e.target.value),\n                                placeholder: \"请输入DeepSeek API密钥\",\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"input pr-10\", errors.deepseek && \"border-red-500\")\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\AIConfigCard.tsx\",\n                                lineNumber: 181,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>toggleKeyVisibility(\"deepseek\"),\n                                className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600\",\n                                children: showKeys.deepseek ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bot_Eye_EyeOff_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\AIConfigCard.tsx\",\n                                    lineNumber: 193,\n                                    columnNumber: 36\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bot_Eye_EyeOff_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\AIConfigCard.tsx\",\n                                    lineNumber: 193,\n                                    columnNumber: 69\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\AIConfigCard.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\AIConfigCard.tsx\",\n                        lineNumber: 180,\n                        columnNumber: 11\n                    }, this),\n                    errors.deepseek && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-1 text-sm text-red-600\",\n                        children: errors.deepseek\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\AIConfigCard.tsx\",\n                        lineNumber: 197,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-1 text-xs text-gray-500\",\n                        children: [\n                            \"获取地址：\",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"https://platform.deepseek.com/api_keys\",\n                                target: \"_blank\",\n                                rel: \"noopener noreferrer\",\n                                className: \"text-bili-blue hover:underline\",\n                                children: \"https://platform.deepseek.com/api_keys\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\AIConfigCard.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 18\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\AIConfigCard.tsx\",\n                        lineNumber: 199,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\AIConfigCard.tsx\",\n                lineNumber: 176,\n                columnNumber: 9\n            }, this),\n            config.model_choice === \"2\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                        children: \"Gemini API密钥\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\AIConfigCard.tsx\",\n                        lineNumber: 208,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: showKeys.gemini ? \"text\" : \"password\",\n                                value: config.api_key_gemini || \"\",\n                                onChange: (e)=>handleGeminiKeyChange(e.target.value),\n                                placeholder: \"请输入Gemini API密钥\",\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"input pr-10\", errors.gemini && \"border-red-500\")\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\AIConfigCard.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>toggleKeyVisibility(\"gemini\"),\n                                className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600\",\n                                children: showKeys.gemini ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bot_Eye_EyeOff_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\AIConfigCard.tsx\",\n                                    lineNumber: 224,\n                                    columnNumber: 34\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bot_Eye_EyeOff_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\AIConfigCard.tsx\",\n                                    lineNumber: 224,\n                                    columnNumber: 67\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\AIConfigCard.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\AIConfigCard.tsx\",\n                        lineNumber: 211,\n                        columnNumber: 11\n                    }, this),\n                    errors.gemini && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-1 text-sm text-red-600\",\n                        children: errors.gemini\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\AIConfigCard.tsx\",\n                        lineNumber: 228,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-1 text-xs text-gray-500\",\n                        children: [\n                            \"获取地址：\",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"https://aistudio.google.com/app/apikey\",\n                                target: \"_blank\",\n                                rel: \"noopener noreferrer\",\n                                className: \"text-bili-blue hover:underline\",\n                                children: \"https://aistudio.google.com/app/apikey\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\AIConfigCard.tsx\",\n                                lineNumber: 231,\n                                columnNumber: 18\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\AIConfigCard.tsx\",\n                        lineNumber: 230,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\AIConfigCard.tsx\",\n                lineNumber: 207,\n                columnNumber: 9\n            }, this),\n            config.model_choice === \"3\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                children: \"API基础URL\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\AIConfigCard.tsx\",\n                                lineNumber: 240,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                value: config.openai_config?.base_url || \"\",\n                                onChange: (e)=>handleOpenAIConfigChange(\"base_url\", e.target.value),\n                                placeholder: \"例如：https://ark.cn-beijing.volces.com/api/v3\",\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"input\", errors.openai && \"border-red-500\")\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\AIConfigCard.tsx\",\n                                lineNumber: 243,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\AIConfigCard.tsx\",\n                        lineNumber: 239,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                children: \"模型名称\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\AIConfigCard.tsx\",\n                                lineNumber: 252,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                value: config.openai_config?.model || \"\",\n                                onChange: (e)=>handleOpenAIConfigChange(\"model\", e.target.value),\n                                placeholder: \"例如：deepseek-v3-250324\",\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"input\", errors.openai && \"border-red-500\")\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\AIConfigCard.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-1 text-xs text-gray-500\",\n                                children: \"不建议使用思考模型，可能产生意想不到的问题\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\AIConfigCard.tsx\",\n                                lineNumber: 262,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\AIConfigCard.tsx\",\n                        lineNumber: 251,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                children: \"API密钥\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\AIConfigCard.tsx\",\n                                lineNumber: 267,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: showKeys.openai ? \"text\" : \"password\",\n                                        value: config.openai_config?.api_key || \"\",\n                                        onChange: (e)=>handleOpenAIConfigChange(\"api_key\", e.target.value),\n                                        placeholder: \"请输入API密钥\",\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"input pr-10\", errors.openai && \"border-red-500\")\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\AIConfigCard.tsx\",\n                                        lineNumber: 271,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: ()=>toggleKeyVisibility(\"openai\"),\n                                        className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600\",\n                                        children: showKeys.openai ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bot_Eye_EyeOff_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\AIConfigCard.tsx\",\n                                            lineNumber: 283,\n                                            columnNumber: 36\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bot_Eye_EyeOff_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\AIConfigCard.tsx\",\n                                            lineNumber: 283,\n                                            columnNumber: 69\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\AIConfigCard.tsx\",\n                                        lineNumber: 278,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\AIConfigCard.tsx\",\n                                lineNumber: 270,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\AIConfigCard.tsx\",\n                        lineNumber: 266,\n                        columnNumber: 11\n                    }, this),\n                    errors.openai && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-red-600\",\n                        children: errors.openai\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\AIConfigCard.tsx\",\n                        lineNumber: 288,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\AIConfigCard.tsx\",\n                lineNumber: 238,\n                columnNumber: 9\n            }, this),\n            errors.general && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4 p-3 bg-red-50 border border-red-200 rounded-lg flex items-center gap-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bot_Eye_EyeOff_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        className: \"w-4 h-4 text-red-500\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\AIConfigCard.tsx\",\n                        lineNumber: 296,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm text-red-600\",\n                        children: errors.general\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\AIConfigCard.tsx\",\n                        lineNumber: 297,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\AIConfigCard.tsx\",\n                lineNumber: 295,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: handleSave,\n                disabled: isLoading,\n                className: \"w-full btn-primary py-3 disabled:opacity-50 disabled:cursor-not-allowed\",\n                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-4 h-4 loading-spinner border-white\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\AIConfigCard.tsx\",\n                            lineNumber: 309,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"保存中...\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\AIConfigCard.tsx\",\n                            lineNumber: 310,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\AIConfigCard.tsx\",\n                    lineNumber: 308,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bot_Eye_EyeOff_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            className: \"w-4 h-4\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\AIConfigCard.tsx\",\n                            lineNumber: 314,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"保存配置\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\AIConfigCard.tsx\",\n                            lineNumber: 315,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\AIConfigCard.tsx\",\n                    lineNumber: 313,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\AIConfigCard.tsx\",\n                lineNumber: 302,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\AIConfigCard.tsx\",\n        lineNumber: 109,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/AIConfigCard.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/CaptchaCard.tsx":
/*!****************************************!*\
  !*** ./src/components/CaptchaCard.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CaptchaCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_ExternalLink_RefreshCw_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,ExternalLink,RefreshCw,Shield!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_ExternalLink_RefreshCw_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,ExternalLink,RefreshCw,Shield!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_ExternalLink_RefreshCw_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,ExternalLink,RefreshCw,Shield!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_ExternalLink_RefreshCw_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,ExternalLink,RefreshCw,Shield!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_ExternalLink_RefreshCw_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,ExternalLink,RefreshCw,Shield!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _lib_quiz__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/quiz */ \"(ssr)/./src/lib/quiz.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction CaptchaCard({ onCaptchaComplete, className }) {\n    const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"loading\");\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedCategories, setSelectedCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [captchaData, setCaptchaData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [captchaCode, setCaptchaCode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // 获取分类信息\n    const fetchCategories = async ()=>{\n        try {\n            setStatus(\"loading\");\n            setError(\"\");\n            const categoryList = await (0,_lib_quiz__WEBPACK_IMPORTED_MODULE_2__.getCategories)();\n            setCategories(categoryList);\n            setStatus(\"selecting\");\n        } catch (err) {\n            setError(err instanceof Error ? err.message : \"获取分类失败\");\n            setStatus(\"error\");\n        }\n    };\n    // 获取验证码\n    const fetchCaptcha = async ()=>{\n        try {\n            setStatus(\"loading\");\n            setError(\"\");\n            const data = await (0,_lib_quiz__WEBPACK_IMPORTED_MODULE_2__.getCaptcha)();\n            setCaptchaData(data);\n            setStatus(\"captcha\");\n        } catch (err) {\n            setError(err instanceof Error ? err.message : \"获取验证码失败\");\n            setStatus(\"error\");\n        }\n    };\n    // 处理分类选择\n    const handleCategoryToggle = (categoryId)=>{\n        setSelectedCategories((prev)=>{\n            const isSelected = prev.includes(categoryId);\n            if (isSelected) {\n                return prev.filter((id)=>id !== categoryId);\n            } else {\n                // 最多选择3个分类\n                if (prev.length >= 3) {\n                    return prev;\n                }\n                return [\n                    ...prev,\n                    categoryId\n                ];\n            }\n        });\n    };\n    // 确认分类选择\n    const handleCategoryConfirm = ()=>{\n        if (selectedCategories.length === 0) {\n            setError(\"请至少选择一个分类\");\n            return;\n        }\n        fetchCaptcha();\n    };\n    // 提交验证码\n    const handleCaptchaSubmit = async ()=>{\n        if (!captchaCode.trim()) {\n            setError(\"请输入验证码\");\n            return;\n        }\n        if (!captchaData) {\n            setError(\"验证码数据丢失，请重新获取\");\n            return;\n        }\n        try {\n            setStatus(\"submitting\");\n            setError(\"\");\n            const categoryIds = selectedCategories.join(\",\");\n            const success = await (0,_lib_quiz__WEBPACK_IMPORTED_MODULE_2__.submitCaptcha)(captchaCode, captchaData.token, categoryIds);\n            if (success) {\n                onCaptchaComplete(categoryIds);\n            } else {\n                throw new Error(\"验证码验证失败\");\n            }\n        } catch (err) {\n            setError(err instanceof Error ? err.message : \"验证码提交失败\");\n            setStatus(\"captcha\");\n        }\n    };\n    // 重新获取验证码\n    const handleRefreshCaptcha = ()=>{\n        setCaptchaCode(\"\");\n        fetchCaptcha();\n    };\n    // 重试\n    const handleRetry = ()=>{\n        setError(\"\");\n        if (status === \"error\" && categories.length === 0) {\n            fetchCategories();\n        } else if (status === \"error\" && !captchaData) {\n            fetchCaptcha();\n        } else {\n            setStatus(\"captcha\");\n        }\n    };\n    // 组件挂载时获取分类\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchCategories();\n    }, []);\n    const getStatusText = ()=>{\n        switch(status){\n            case \"loading\":\n                return \"正在加载...\";\n            case \"selecting\":\n                return \"请选择答题分类\";\n            case \"captcha\":\n                return \"请输入验证码\";\n            case \"submitting\":\n                return \"正在验证...\";\n            case \"error\":\n                return \"发生错误\";\n            default:\n                return \"\";\n        }\n    };\n    const getStatusIcon = ()=>{\n        switch(status){\n            case \"loading\":\n            case \"submitting\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-5 h-5 loading-spinner\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\CaptchaCard.tsx\",\n                    lineNumber: 153,\n                    columnNumber: 16\n                }, this);\n            case \"selecting\":\n            case \"captcha\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_ExternalLink_RefreshCw_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"w-5 h-5 text-bili-blue\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\CaptchaCard.tsx\",\n                    lineNumber: 156,\n                    columnNumber: 16\n                }, this);\n            case \"error\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_ExternalLink_RefreshCw_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"w-5 h-5 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\CaptchaCard.tsx\",\n                    lineNumber: 158,\n                    columnNumber: 16\n                }, this);\n            default:\n                return null;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"card max-w-2xl mx-auto\", className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center gap-2 mb-2\",\n                        children: [\n                            getStatusIcon(),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: \"验证码验证\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\CaptchaCard.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\CaptchaCard.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: getStatusText()\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\CaptchaCard.tsx\",\n                        lineNumber: 172,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\CaptchaCard.tsx\",\n                lineNumber: 167,\n                columnNumber: 7\n            }, this),\n            status === \"selecting\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-gray-900 mb-3\",\n                                children: \"选择答题分类\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\CaptchaCard.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-600 mb-4\",\n                                children: \"请选择1-3个分类（最多3个），建议选择知识区和历史区以获得更高正确率\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\CaptchaCard.tsx\",\n                                lineNumber: 180,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 gap-3\",\n                                children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleCategoryToggle(category.id),\n                                        disabled: !selectedCategories.includes(category.id) && selectedCategories.length >= 3,\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"p-3 text-left border rounded-lg transition-colors\", \"hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\", selectedCategories.includes(category.id) ? \"border-bili-pink bg-bili-pink/5\" : \"border-gray-200\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                selectedCategories.includes(category.id) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_ExternalLink_RefreshCw_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"w-4 h-4 text-bili-pink\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\CaptchaCard.tsx\",\n                                                    lineNumber: 199,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: category.name\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\CaptchaCard.tsx\",\n                                                    lineNumber: 201,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\CaptchaCard.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, category.id, false, {\n                                        fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\CaptchaCard.tsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\CaptchaCard.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\CaptchaCard.tsx\",\n                        lineNumber: 178,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleCategoryConfirm,\n                            disabled: selectedCategories.length === 0,\n                            className: \"btn-primary px-6 py-2 disabled:opacity-50 disabled:cursor-not-allowed\",\n                            children: [\n                                \"确认选择 (\",\n                                selectedCategories.length,\n                                \"/3)\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\CaptchaCard.tsx\",\n                            lineNumber: 209,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\CaptchaCard.tsx\",\n                        lineNumber: 208,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\CaptchaCard.tsx\",\n                lineNumber: 177,\n                columnNumber: 9\n            }, this),\n            status === \"captcha\" && captchaData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-gray-900 mb-3\",\n                                children: \"输入验证码\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\CaptchaCard.tsx\",\n                                lineNumber: 224,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3 mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"验证码图片：\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\CaptchaCard.tsx\",\n                                                lineNumber: 229,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleRefreshCaptcha,\n                                                className: \"btn-outline px-2 py-1 text-xs\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_ExternalLink_RefreshCw_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"w-3 h-3 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\CaptchaCard.tsx\",\n                                                        lineNumber: 234,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"刷新\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\CaptchaCard.tsx\",\n                                                lineNumber: 230,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\CaptchaCard.tsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: captchaData.url,\n                                        target: \"_blank\",\n                                        rel: \"noopener noreferrer\",\n                                        className: \"inline-flex items-center gap-2 p-3 bg-gray-50 border border-gray-200 rounded-lg hover:bg-gray-100 transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_ExternalLink_RefreshCw_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-4 h-4 text-bili-blue\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\CaptchaCard.tsx\",\n                                                lineNumber: 244,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-bili-blue\",\n                                                children: \"点击查看验证码\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\CaptchaCard.tsx\",\n                                                lineNumber: 245,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\CaptchaCard.tsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\CaptchaCard.tsx\",\n                                lineNumber: 227,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                        children: \"验证码\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\CaptchaCard.tsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        value: captchaCode,\n                                        onChange: (e)=>setCaptchaCode(e.target.value),\n                                        placeholder: \"请输入验证码\",\n                                        className: \"input\",\n                                        disabled: false,\n                                        onKeyPress: (e)=>{\n                                            if (e.key === \"Enter\") {\n                                                handleCaptchaSubmit();\n                                            }\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\CaptchaCard.tsx\",\n                                        lineNumber: 254,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\CaptchaCard.tsx\",\n                                lineNumber: 250,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\CaptchaCard.tsx\",\n                        lineNumber: 223,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleCaptchaSubmit,\n                            disabled: !captchaCode.trim(),\n                            className: \"btn-primary px-6 py-2 disabled:opacity-50 disabled:cursor-not-allowed\",\n                            children:  false ? /*#__PURE__*/ 0 : \"提交验证码\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\CaptchaCard.tsx\",\n                            lineNumber: 271,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\CaptchaCard.tsx\",\n                        lineNumber: 270,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\CaptchaCard.tsx\",\n                lineNumber: 222,\n                columnNumber: 9\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4 p-4 bg-red-50 border border-red-200 rounded-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_ExternalLink_RefreshCw_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"w-4 h-4 text-red-500\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\CaptchaCard.tsx\",\n                                lineNumber: 293,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-medium text-red-800\",\n                                children: \"错误\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\CaptchaCard.tsx\",\n                                lineNumber: 294,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\CaptchaCard.tsx\",\n                        lineNumber: 292,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-red-700 mb-2\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\CaptchaCard.tsx\",\n                        lineNumber: 296,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleRetry,\n                        className: \"btn-outline px-3 py-1 text-sm\",\n                        children: \"重试\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\CaptchaCard.tsx\",\n                        lineNumber: 297,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\CaptchaCard.tsx\",\n                lineNumber: 291,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6 p-3 bg-blue-50 border border-blue-200 rounded-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm text-blue-800\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"font-medium mb-1\",\n                            children: \"提示：\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\CaptchaCard.tsx\",\n                            lineNumber: 309,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            className: \"space-y-1 text-xs\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"• 验证码用于确认您的答题分类选择\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\CaptchaCard.tsx\",\n                                    lineNumber: 311,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"• 建议选择知识区和历史区，正确率更高\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\CaptchaCard.tsx\",\n                                    lineNumber: 312,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"• 每日最多可以答题3次\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\CaptchaCard.tsx\",\n                                    lineNumber: 313,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\CaptchaCard.tsx\",\n                            lineNumber: 310,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\CaptchaCard.tsx\",\n                    lineNumber: 308,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\CaptchaCard.tsx\",\n                lineNumber: 307,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\CaptchaCard.tsx\",\n        lineNumber: 165,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/CaptchaCard.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ErrorBoundary.tsx":
/*!******************************************!*\
  !*** ./src/components/ErrorBoundary.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,RefreshCw!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,RefreshCw!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nclass ErrorBoundary extends (react__WEBPACK_IMPORTED_MODULE_1___default().Component) {\n    constructor(props){\n        super(props);\n        this.resetError = ()=>{\n            this.setState({\n                hasError: false,\n                error: undefined\n            });\n        };\n        this.state = {\n            hasError: false\n        };\n    }\n    static getDerivedStateFromError(error) {\n        return {\n            hasError: true,\n            error\n        };\n    }\n    componentDidCatch(error, errorInfo) {\n        console.error(\"ErrorBoundary caught an error:\", error, errorInfo);\n    }\n    render() {\n        if (this.state.hasError) {\n            if (this.props.fallback) {\n                const FallbackComponent = this.props.fallback;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FallbackComponent, {\n                    error: this.state.error,\n                    resetError: this.resetError\n                }, void 0, false, {\n                    fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                    lineNumber: 38,\n                    columnNumber: 16\n                }, this);\n            }\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DefaultErrorFallback, {\n                error: this.state.error,\n                resetError: this.resetError\n            }, void 0, false, {\n                fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                lineNumber: 41,\n                columnNumber: 14\n            }, this);\n        }\n        return this.props.children;\n    }\n}\nfunction DefaultErrorFallback({ error, resetError }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md mx-auto text-center p-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        className: \"w-8 h-8 text-red-600\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                    lineNumber: 52,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-2xl font-bold text-gray-900 mb-2\",\n                    children: \"出现了一些问题\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-600 mb-6\",\n                    children: \"应用遇到了意外错误，请尝试刷新页面或重新开始。\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 9\n                }, this),\n                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"details\", {\n                    className: \"mb-6 text-left\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"summary\", {\n                            className: \"cursor-pointer text-sm text-gray-500 hover:text-gray-700\",\n                            children: \"查看错误详情\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 66,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                            className: \"mt-2 p-3 bg-gray-100 rounded text-xs text-gray-700 overflow-auto\",\n                            children: [\n                                error.message,\n                                error.stack && \"\\n\\n\" + error.stack\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 69,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                    lineNumber: 65,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: resetError,\n                            className: \"w-full btn-primary py-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    className: \"w-4 h-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 13\n                                }, this),\n                                \"重试\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 77,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>window.location.reload(),\n                            className: \"w-full btn-outline py-2\",\n                            children: \"刷新页面\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 85,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                    lineNumber: 76,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"mt-6 text-xs text-gray-500\",\n                    children: \"如果问题持续存在，请尝试清除浏览器缓存或联系技术支持。\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                    lineNumber: 93,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n            lineNumber: 51,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n        lineNumber: 50,\n        columnNumber: 5\n    }, this);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ErrorBoundary);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ErrorBoundary.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/LoadingSpinner.tsx":
/*!*******************************************!*\
  !*** ./src/components/LoadingSpinner.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LoadingSpinner)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction LoadingSpinner({ size = \"md\", className, text }) {\n    const sizeClasses = {\n        sm: \"w-4 h-4\",\n        md: \"w-6 h-6\",\n        lg: \"w-8 h-8\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"flex items-center justify-center\", className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"loading-spinner mx-auto\", sizeClasses[size])\n                }, void 0, false, {\n                    fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\LoadingSpinner.tsx\",\n                    lineNumber: 21,\n                    columnNumber: 9\n                }, this),\n                text && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"mt-2 text-sm text-gray-600\",\n                    children: text\n                }, void 0, false, {\n                    fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\LoadingSpinner.tsx\",\n                    lineNumber: 23,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\LoadingSpinner.tsx\",\n            lineNumber: 20,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\LoadingSpinner.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9Mb2FkaW5nU3Bpbm5lci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFFaUM7QUFRbEIsU0FBU0MsZUFBZSxFQUFFQyxPQUFPLElBQUksRUFBRUMsU0FBUyxFQUFFQyxJQUFJLEVBQXVCO0lBQzFGLE1BQU1DLGNBQWM7UUFDbEJDLElBQUk7UUFDSkMsSUFBSTtRQUNKQyxJQUFJO0lBQ047SUFFQSxxQkFDRSw4REFBQ0M7UUFBSU4sV0FBV0gsOENBQUVBLENBQUMsb0NBQW9DRztrQkFDckQsNEVBQUNNO1lBQUlOLFdBQVU7OzhCQUNiLDhEQUFDTTtvQkFBSU4sV0FBV0gsOENBQUVBLENBQUMsMkJBQTJCSyxXQUFXLENBQUNILEtBQUs7Ozs7OztnQkFDOURFLHNCQUNDLDhEQUFDTTtvQkFBRVAsV0FBVTs4QkFBOEJDOzs7Ozs7Ozs7Ozs7Ozs7OztBQUtyRCIsInNvdXJjZXMiOlsid2VicGFjazovL2JpbGktaGFyZGNvcmUtd2ViLy4vc3JjL2NvbXBvbmVudHMvTG9hZGluZ1NwaW5uZXIudHN4P2Q2NzgiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgeyBjbiB9IGZyb20gJ0AvbGliL3V0aWxzJztcblxuaW50ZXJmYWNlIExvYWRpbmdTcGlubmVyUHJvcHMge1xuICBzaXplPzogJ3NtJyB8ICdtZCcgfCAnbGcnO1xuICBjbGFzc05hbWU/OiBzdHJpbmc7XG4gIHRleHQ/OiBzdHJpbmc7XG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIExvYWRpbmdTcGlubmVyKHsgc2l6ZSA9ICdtZCcsIGNsYXNzTmFtZSwgdGV4dCB9OiBMb2FkaW5nU3Bpbm5lclByb3BzKSB7XG4gIGNvbnN0IHNpemVDbGFzc2VzID0ge1xuICAgIHNtOiAndy00IGgtNCcsXG4gICAgbWQ6ICd3LTYgaC02JyxcbiAgICBsZzogJ3ctOCBoLTgnLFxuICB9O1xuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9e2NuKCdmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlcicsIGNsYXNzTmFtZSl9PlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT17Y24oJ2xvYWRpbmctc3Bpbm5lciBteC1hdXRvJywgc2l6ZUNsYXNzZXNbc2l6ZV0pfSAvPlxuICAgICAgICB7dGV4dCAmJiAoXG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwibXQtMiB0ZXh0LXNtIHRleHQtZ3JheS02MDBcIj57dGV4dH08L3A+XG4gICAgICAgICl9XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJjbiIsIkxvYWRpbmdTcGlubmVyIiwic2l6ZSIsImNsYXNzTmFtZSIsInRleHQiLCJzaXplQ2xhc3NlcyIsInNtIiwibWQiLCJsZyIsImRpdiIsInAiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/LoadingSpinner.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/LoginCard.tsx":
/*!**************************************!*\
  !*** ./src/components/LoginCard.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LoginCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_QrCode_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,QrCode,RefreshCw!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/qr-code.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_QrCode_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,QrCode,RefreshCw!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_QrCode_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,QrCode,RefreshCw!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_QrCode_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,QrCode,RefreshCw!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(ssr)/./src/lib/auth.ts\");\n/* harmony import */ var _lib_qrcode__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/qrcode */ \"(ssr)/./src/lib/qrcode.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction LoginCard({ onLoginSuccess, className }) {\n    const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"idle\");\n    const [qrData, setQrData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [qrCodeImage, setQrCodeImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [retryCount, setRetryCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // 生成二维码\n    const generateQRCode = async ()=>{\n        try {\n            setStatus(\"loading\");\n            setError(\"\");\n            const data = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.getQRCode)();\n            setQrData(data);\n            const imageUrl = await (0,_lib_qrcode__WEBPACK_IMPORTED_MODULE_3__.generateQRCodeDataURL)(data.url);\n            setQrCodeImage(imageUrl);\n            setStatus(\"waiting\");\n            startPolling(data.auth_code);\n        } catch (err) {\n            setError(\"获取二维码失败，请重试\");\n            setStatus(\"error\");\n        }\n    };\n    // 开始轮询二维码状态\n    const startPolling = (authCode)=>{\n        let pollCount = 0;\n        const maxPolls = 60; // 最大轮询次数（60秒）\n        const pollInterval = setInterval(async ()=>{\n            try {\n                if (pollCount >= maxPolls) {\n                    clearInterval(pollInterval);\n                    setStatus(\"expired\");\n                    return;\n                }\n                const result = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.pollQRCode)(authCode);\n                if (result.code === 0 && result.data) {\n                    // 登录成功\n                    clearInterval(pollInterval);\n                    setStatus(\"success\");\n                    // 构建认证数据\n                    const cookies = result.data.cookie_info.cookies;\n                    let csrf = \"\";\n                    for (const cookie of cookies){\n                        if (cookie.name === \"bili_jct\") {\n                            csrf = cookie.value;\n                            break;\n                        }\n                    }\n                    const cookieString = cookies.map((cookie)=>`${cookie.name}=${cookie.value}`).join(\";\");\n                    const authData = {\n                        access_token: result.data.access_token,\n                        csrf,\n                        mid: String(result.data.mid),\n                        cookie: cookieString\n                    };\n                    (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.saveAuthData)(authData);\n                    onLoginSuccess(authData);\n                } else if (result.code === 86101) {\n                    // 二维码未扫描，继续轮询\n                    pollCount++;\n                } else if (result.code === 86090) {\n                    // 二维码已扫描但未确认\n                    setStatus(\"scanned\");\n                    pollCount++;\n                } else if (result.code === 86038) {\n                    // 二维码已过期\n                    clearInterval(pollInterval);\n                    setStatus(\"expired\");\n                } else {\n                    // 其他错误\n                    clearInterval(pollInterval);\n                    setError(result.message || \"登录失败\");\n                    setStatus(\"error\");\n                }\n            } catch (err) {\n                pollCount++;\n                if (pollCount >= maxPolls) {\n                    clearInterval(pollInterval);\n                    setError(\"网络连接超时，请重试\");\n                    setStatus(\"error\");\n                }\n            }\n        }, 1000);\n    };\n    // 重新生成二维码\n    const handleRefresh = ()=>{\n        setRetryCount((prev)=>prev + 1);\n        generateQRCode();\n    };\n    // 组件挂载时生成二维码\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        generateQRCode();\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, []);\n    const getStatusText = ()=>{\n        switch(status){\n            case \"loading\":\n                return \"正在生成二维码...\";\n            case \"waiting\":\n                return \"请使用哔哩哔哩APP扫描二维码登录\";\n            case \"scanned\":\n                return \"扫描成功，请在手机上确认登录\";\n            case \"success\":\n                return \"登录成功！\";\n            case \"expired\":\n                return \"二维码已过期，请点击刷新\";\n            case \"error\":\n                return error || \"发生错误，请重试\";\n            default:\n                return \"\";\n        }\n    };\n    const getStatusIcon = ()=>{\n        switch(status){\n            case \"loading\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-5 h-5 loading-spinner\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\LoginCard.tsx\",\n                    lineNumber: 150,\n                    columnNumber: 16\n                }, this);\n            case \"waiting\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_QrCode_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"w-5 h-5 text-bili-blue\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\LoginCard.tsx\",\n                    lineNumber: 152,\n                    columnNumber: 16\n                }, this);\n            case \"scanned\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_QrCode_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"w-5 h-5 text-yellow-500\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\LoginCard.tsx\",\n                    lineNumber: 154,\n                    columnNumber: 16\n                }, this);\n            case \"success\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_QrCode_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"w-5 h-5 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\LoginCard.tsx\",\n                    lineNumber: 156,\n                    columnNumber: 16\n                }, this);\n            case \"expired\":\n            case \"error\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_QrCode_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"w-5 h-5 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\LoginCard.tsx\",\n                    lineNumber: 159,\n                    columnNumber: 16\n                }, this);\n            default:\n                return null;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"card max-w-md mx-auto\", className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-2xl font-bold text-gray-900 mb-6\",\n                    children: \"哔哩哔哩登录\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\LoginCard.tsx\",\n                    lineNumber: 168,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative inline-block\",\n                        children: [\n                            qrCodeImage && status !== \"loading\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                src: qrCodeImage,\n                                alt: \"登录二维码\",\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"w-64 h-64 border-2 border-gray-200 rounded-lg\", status === \"expired\" && \"opacity-50 grayscale\")\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\LoginCard.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-64 h-64 border-2 border-gray-200 rounded-lg flex items-center justify-center bg-gray-50\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-8 loading-spinner\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\LoginCard.tsx\",\n                                    lineNumber: 186,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\LoginCard.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 15\n                            }, this),\n                            (status === \"expired\" || status === \"error\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleRefresh,\n                                className: \"absolute inset-0 flex items-center justify-center bg-black bg-opacity-50 rounded-lg text-white hover:bg-opacity-60 transition-colors\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_QrCode_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"w-8 h-8\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\LoginCard.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\LoginCard.tsx\",\n                                lineNumber: 192,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\LoginCard.tsx\",\n                        lineNumber: 174,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\LoginCard.tsx\",\n                    lineNumber: 173,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center gap-2 mb-4\",\n                    children: [\n                        getStatusIcon(),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"text-sm\", status === \"error\" || status === \"expired\" ? \"text-red-600\" : status === \"success\" ? \"text-green-600\" : status === \"scanned\" ? \"text-yellow-600\" : \"text-gray-600\"),\n                            children: getStatusText()\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\LoginCard.tsx\",\n                            lineNumber: 205,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\LoginCard.tsx\",\n                    lineNumber: 203,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-xs text-gray-500 space-y-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"1. 打开哔哩哔哩APP\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\LoginCard.tsx\",\n                            lineNumber: 218,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: '2. 点击右下角\"我的\"'\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\LoginCard.tsx\",\n                            lineNumber: 219,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"3. 点击右上角扫码图标\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\LoginCard.tsx\",\n                            lineNumber: 220,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"4. 扫描上方二维码完成登录\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\LoginCard.tsx\",\n                            lineNumber: 221,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\LoginCard.tsx\",\n                    lineNumber: 217,\n                    columnNumber: 9\n                }, this),\n                status !== \"loading\" && status !== \"success\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handleRefresh,\n                    className: \"mt-4 btn-outline px-4 py-2 text-sm\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_QrCode_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            className: \"w-4 h-4 mr-2\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\LoginCard.tsx\",\n                            lineNumber: 230,\n                            columnNumber: 13\n                        }, this),\n                        \"刷新二维码\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\LoginCard.tsx\",\n                    lineNumber: 226,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\LoginCard.tsx\",\n            lineNumber: 167,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\LoginCard.tsx\",\n        lineNumber: 166,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/LoginCard.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/QuizCard.tsx":
/*!*************************************!*\
  !*** ./src/components/QuizCard.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ QuizCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Brain_CheckCircle_Clock_Trophy_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Brain,CheckCircle,Clock,Trophy,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Brain_CheckCircle_Clock_Trophy_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Brain,CheckCircle,Clock,Trophy,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trophy.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Brain_CheckCircle_Clock_Trophy_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Brain,CheckCircle,Clock,Trophy,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Brain_CheckCircle_Clock_Trophy_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Brain,CheckCircle,Clock,Trophy,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Brain_CheckCircle_Clock_Trophy_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Brain,CheckCircle,Clock,Trophy,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Brain_CheckCircle_Clock_Trophy_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Brain,CheckCircle,Clock,Trophy,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x-circle.js\");\n/* harmony import */ var _lib_quiz__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/quiz */ \"(ssr)/./src/lib/quiz.ts\");\n/* harmony import */ var _lib_ai__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/ai */ \"(ssr)/./src/lib/ai.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction QuizCard({ aiConfig, onQuizComplete, className }) {\n    const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"idle\");\n    const [currentQuestion, setCurrentQuestion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [questionNum, setQuestionNum] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [currentScore, setCurrentScore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [aiAnswer, setAiAnswer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedAnswer, setSelectedAnswer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isAutoMode, setIsAutoMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // 获取下一题\n    const fetchNextQuestion = async ()=>{\n        try {\n            setStatus(\"loading\");\n            setError(\"\");\n            setAiAnswer(\"\");\n            setSelectedAnswer(null);\n            const response = await (0,_lib_quiz__WEBPACK_IMPORTED_MODULE_2__.getQuestion)();\n            if (response.code !== 0) {\n                if (response.code === 41103) {\n                    setError(\"答题已结束或您已经是硬核会员\");\n                    setStatus(\"completed\");\n                    return;\n                }\n                throw new Error(response.message || \"获取题目失败\");\n            }\n            if (!response.data) {\n                throw new Error(\"题目数据为空\");\n            }\n            setCurrentQuestion(response.data);\n            setQuestionNum(response.data.question_num);\n            setStatus(\"answering\");\n            // 如果是自动模式，调用AI回答\n            if (isAutoMode) {\n                await getAIAnswer(response.data);\n            }\n        } catch (err) {\n            setError(err instanceof Error ? err.message : \"获取题目失败\");\n            setStatus(\"error\");\n        }\n    };\n    // 获取AI答案\n    const getAIAnswer = async (question)=>{\n        try {\n            const aiInstance = (0,_lib_ai__WEBPACK_IMPORTED_MODULE_3__.createAIInstance)(aiConfig);\n            const questionText = (0,_lib_quiz__WEBPACK_IMPORTED_MODULE_2__.formatQuestionForAI)(question);\n            const response = await aiInstance.ask(questionText);\n            setAiAnswer(response);\n            // 解析AI回答\n            const answerIndex = (0,_lib_quiz__WEBPACK_IMPORTED_MODULE_2__.parseAIAnswer)(response, question.answers.length);\n            if (answerIndex && answerIndex >= 1 && answerIndex <= question.answers.length) {\n                const answer = question.answers[answerIndex - 1];\n                setSelectedAnswer(answer);\n                // 自动提交答案\n                if (isAutoMode) {\n                    setTimeout(()=>{\n                        submitSelectedAnswer(question.id, answer);\n                    }, 1000); // 延迟1秒提交，让用户看到AI的选择\n                }\n            } else {\n                setError(`AI回答无效: ${response}`);\n            }\n        } catch (err) {\n            setError(`AI回答失败: ${err instanceof Error ? err.message : \"未知错误\"}`);\n        }\n    };\n    // 提交选中的答案\n    const submitSelectedAnswer = async (questionId, answer)=>{\n        try {\n            setStatus(\"submitting\");\n            const response = await (0,_lib_quiz__WEBPACK_IMPORTED_MODULE_2__.submitAnswer)(questionId, answer.ans_hash, answer.ans_text);\n            if (response.code === 0) {\n                // 获取最新分数\n                const result = await (0,_lib_quiz__WEBPACK_IMPORTED_MODULE_2__.getQuizResult)();\n                const newScore = result.score;\n                if (newScore > currentScore) {\n                    setCurrentScore(newScore);\n                    // 答对了，继续下一题\n                    setTimeout(fetchNextQuestion, 2000);\n                } else {\n                    // 答错了，也继续下一题\n                    setTimeout(fetchNextQuestion, 2000);\n                }\n            } else if (response.code === 41103) {\n                // 答题结束\n                setStatus(\"completed\");\n                const finalResult = await (0,_lib_quiz__WEBPACK_IMPORTED_MODULE_2__.getQuizResult)();\n                onQuizComplete(finalResult);\n            } else {\n                throw new Error(response.message || \"提交答案失败\");\n            }\n        } catch (err) {\n            setError(err instanceof Error ? err.message : \"提交答案失败\");\n            setStatus(\"error\");\n        }\n    };\n    // 手动选择答案\n    const handleManualAnswer = (answer)=>{\n        if (!currentQuestion || status === \"submitting\") return;\n        setSelectedAnswer(answer);\n        if (!isAutoMode) {\n            submitSelectedAnswer(currentQuestion.id, answer);\n        }\n    };\n    // 开始答题\n    const startQuiz = ()=>{\n        setQuestionNum(0);\n        setCurrentScore(0);\n        fetchNextQuestion();\n    };\n    // 重试\n    const handleRetry = ()=>{\n        setError(\"\");\n        if (currentQuestion) {\n            if (isAutoMode) {\n                getAIAnswer(currentQuestion);\n            } else {\n                setStatus(\"answering\");\n            }\n        } else {\n            fetchNextQuestion();\n        }\n    };\n    // 组件挂载时开始答题\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        startQuiz();\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, []);\n    const getStatusText = ()=>{\n        switch(status){\n            case \"loading\":\n                return \"正在获取题目...\";\n            case \"answering\":\n                return isAutoMode ? \"AI正在思考...\" : \"请选择答案\";\n            case \"submitting\":\n                return \"正在提交答案...\";\n            case \"completed\":\n                return \"答题已完成\";\n            case \"error\":\n                return \"发生错误\";\n            default:\n                return \"\";\n        }\n    };\n    const getStatusIcon = ()=>{\n        switch(status){\n            case \"loading\":\n            case \"submitting\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-5 h-5 loading-spinner\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\QuizCard.tsx\",\n                    lineNumber: 186,\n                    columnNumber: 16\n                }, this);\n            case \"answering\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Brain_CheckCircle_Clock_Trophy_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"w-5 h-5 text-bili-blue\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\QuizCard.tsx\",\n                    lineNumber: 188,\n                    columnNumber: 16\n                }, this);\n            case \"completed\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Brain_CheckCircle_Clock_Trophy_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"w-5 h-5 text-yellow-500\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\QuizCard.tsx\",\n                    lineNumber: 190,\n                    columnNumber: 16\n                }, this);\n            case \"error\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Brain_CheckCircle_Clock_Trophy_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"w-5 h-5 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\QuizCard.tsx\",\n                    lineNumber: 192,\n                    columnNumber: 16\n                }, this);\n            default:\n                return null;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"card max-w-4xl mx-auto\", className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    getStatusIcon(),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-bold text-gray-900\",\n                                        children: questionNum > 0 ? `第${questionNum}题` : \"答题系统\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\QuizCard.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\QuizCard.tsx\",\n                                lineNumber: 203,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-500\",\n                                children: [\n                                    \"当前得分: \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium text-bili-pink\",\n                                        children: currentScore\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\QuizCard.tsx\",\n                                        lineNumber: 210,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\QuizCard.tsx\",\n                                lineNumber: 209,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\QuizCard.tsx\",\n                        lineNumber: 202,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"flex items-center gap-2 text-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"checkbox\",\n                                    checked: isAutoMode,\n                                    onChange: (e)=>setIsAutoMode(e.target.checked),\n                                    disabled: status === \"submitting\",\n                                    className: \"rounded\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\QuizCard.tsx\",\n                                    lineNumber: 217,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"AI自动答题\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\QuizCard.tsx\",\n                                    lineNumber: 224,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\QuizCard.tsx\",\n                            lineNumber: 216,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\QuizCard.tsx\",\n                        lineNumber: 215,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\QuizCard.tsx\",\n                lineNumber: 201,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4 p-3 bg-gray-50 rounded-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2 text-sm text-gray-600\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Brain_CheckCircle_Clock_Trophy_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            className: \"w-4 h-4\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\QuizCard.tsx\",\n                            lineNumber: 232,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: getStatusText()\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\QuizCard.tsx\",\n                            lineNumber: 233,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\QuizCard.tsx\",\n                    lineNumber: 231,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\QuizCard.tsx\",\n                lineNumber: 230,\n                columnNumber: 7\n            }, this),\n            currentQuestion && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-gray-900 mb-2\",\n                                children: \"题目\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\QuizCard.tsx\",\n                                lineNumber: 241,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-700 leading-relaxed\",\n                                children: currentQuestion.question\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\QuizCard.tsx\",\n                                lineNumber: 242,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\QuizCard.tsx\",\n                        lineNumber: 240,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-md font-medium text-gray-900\",\n                                children: \"选项\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\QuizCard.tsx\",\n                                lineNumber: 247,\n                                columnNumber: 13\n                            }, this),\n                            currentQuestion.answers.map((answer, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleManualAnswer(answer),\n                                    disabled: status === \"submitting\" || isAutoMode && status === \"answering\",\n                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"w-full p-4 text-left border rounded-lg transition-colors\", \"hover:bg-gray-50 disabled:cursor-not-allowed\", selectedAnswer?.ans_hash === answer.ans_hash ? \"border-bili-pink bg-bili-pink/5\" : \"border-gray-200\"),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"flex-shrink-0 w-6 h-6 bg-gray-100 rounded-full flex items-center justify-center text-sm font-medium\",\n                                                children: index + 1\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\QuizCard.tsx\",\n                                                lineNumber: 262,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-700\",\n                                                children: answer.ans_text\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\QuizCard.tsx\",\n                                                lineNumber: 265,\n                                                columnNumber: 19\n                                            }, this),\n                                            selectedAnswer?.ans_hash === answer.ans_hash && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Brain_CheckCircle_Clock_Trophy_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"flex-shrink-0 w-5 h-5 text-bili-pink ml-auto\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\QuizCard.tsx\",\n                                                lineNumber: 267,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\QuizCard.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 17\n                                    }, this)\n                                }, index, false, {\n                                    fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\QuizCard.tsx\",\n                                    lineNumber: 249,\n                                    columnNumber: 15\n                                }, this))\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\QuizCard.tsx\",\n                        lineNumber: 246,\n                        columnNumber: 11\n                    }, this),\n                    aiAnswer && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Brain_CheckCircle_Clock_Trophy_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-4 h-4 text-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\QuizCard.tsx\",\n                                        lineNumber: 278,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium text-blue-800\",\n                                        children: \"AI回答\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\QuizCard.tsx\",\n                                        lineNumber: 279,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\QuizCard.tsx\",\n                                lineNumber: 277,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-blue-700\",\n                                children: aiAnswer\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\QuizCard.tsx\",\n                                lineNumber: 281,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\QuizCard.tsx\",\n                        lineNumber: 276,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\QuizCard.tsx\",\n                lineNumber: 239,\n                columnNumber: 9\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4 p-4 bg-red-50 border border-red-200 rounded-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Brain_CheckCircle_Clock_Trophy_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"w-4 h-4 text-red-500\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\QuizCard.tsx\",\n                                lineNumber: 291,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-medium text-red-800\",\n                                children: \"错误\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\QuizCard.tsx\",\n                                lineNumber: 292,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\QuizCard.tsx\",\n                        lineNumber: 290,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-red-700\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\QuizCard.tsx\",\n                        lineNumber: 294,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleRetry,\n                        className: \"mt-2 btn-outline px-3 py-1 text-sm\",\n                        children: \"重试\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\QuizCard.tsx\",\n                        lineNumber: 295,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\QuizCard.tsx\",\n                lineNumber: 289,\n                columnNumber: 9\n            }, this),\n            !isAutoMode && selectedAnswer && status === \"answering\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>currentQuestion && submitSelectedAnswer(currentQuestion.id, selectedAnswer),\n                    className: \"btn-primary px-6 py-2\",\n                    children: \"提交答案\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\QuizCard.tsx\",\n                    lineNumber: 307,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\QuizCard.tsx\",\n                lineNumber: 306,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\QuizCard.tsx\",\n        lineNumber: 199,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/QuizCard.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ResultCard.tsx":
/*!***************************************!*\
  !*** ./src/components/ResultCard.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ResultCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_RotateCcw_Star_Trash2_Trophy_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=RotateCcw,Star,Trash2,Trophy,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trophy.js\");\n/* harmony import */ var _barrel_optimize_names_RotateCcw_Star_Trash2_Trophy_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=RotateCcw,Star,Trash2,Trophy,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x-circle.js\");\n/* harmony import */ var _barrel_optimize_names_RotateCcw_Star_Trash2_Trophy_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=RotateCcw,Star,Trash2,Trophy,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_RotateCcw_Star_Trash2_Trophy_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=RotateCcw,Star,Trash2,Trophy,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _barrel_optimize_names_RotateCcw_Star_Trash2_Trophy_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=RotateCcw,Star,Trash2,Trophy,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(ssr)/./src/lib/auth.ts\");\n/* harmony import */ var _lib_ai__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/ai */ \"(ssr)/./src/lib/ai.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nfunction ResultCard({ result, onRestart, className }) {\n    const [showClearDialog, setShowClearDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isClearing, setIsClearing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const isPassed = result.score >= 60;\n    const totalPossibleScore = result.scores.reduce((sum, score)=>sum + score.total, 0);\n    // 清除所有数据\n    const handleClearData = async ()=>{\n        try {\n            setIsClearing(true);\n            // 清除认证信息\n            (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.clearAuthData)();\n            // 清除AI配置\n            (0,_lib_ai__WEBPACK_IMPORTED_MODULE_3__.clearAIConfig)();\n            // 清除其他本地存储数据\n            _lib_utils__WEBPACK_IMPORTED_MODULE_4__.storage.clear();\n            // 延迟一下让用户看到操作完成\n            setTimeout(()=>{\n                setShowClearDialog(false);\n                setIsClearing(false);\n                // 刷新页面\n                window.location.reload();\n            }, 1000);\n        } catch (error) {\n            console.error(\"清除数据失败:\", error);\n            setIsClearing(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"card max-w-2xl mx-auto\", className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center gap-2 mb-4\",\n                        children: isPassed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_RotateCcw_Star_Trash2_Trophy_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"w-8 h-8 text-green-600\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\ResultCard.tsx\",\n                                lineNumber: 58,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\ResultCard.tsx\",\n                            lineNumber: 57,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_RotateCcw_Star_Trash2_Trophy_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"w-8 h-8 text-red-600\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\ResultCard.tsx\",\n                                lineNumber: 62,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\ResultCard.tsx\",\n                            lineNumber: 61,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\ResultCard.tsx\",\n                        lineNumber: 55,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-3xl font-bold text-gray-900 mb-2\",\n                        children: isPassed ? \"\\uD83C\\uDF89 恭喜通过！\" : \"\\uD83D\\uDE14 未能通过\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\ResultCard.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: isPassed ? \"您已成功通过哔哩哔哩硬核会员答题！\" : \"运气稍微有点差，建议重新答题，知识区和历史区的正确率会更高\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\ResultCard.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\ResultCard.tsx\",\n                lineNumber: 54,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-4xl font-bold text-bili-pink mb-1\",\n                                children: result.score\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\ResultCard.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-500\",\n                                children: [\n                                    \"总分 / \",\n                                    totalPossibleScore\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\ResultCard.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\ResultCard.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full bg-gray-200 rounded-full h-3 mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"h-3 rounded-full transition-all duration-1000\", isPassed ? \"bg-green-500\" : \"bg-red-500\"),\n                            style: {\n                                width: `${Math.min(result.score / totalPossibleScore * 100, 100)}%`\n                            }\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\ResultCard.tsx\",\n                            lineNumber: 92,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\ResultCard.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between text-xs text-gray-500 mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"0\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\ResultCard.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"relative\",\n                                children: [\n                                    \"60 (及格线)\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-px h-4 bg-yellow-400\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\ResultCard.tsx\",\n                                            lineNumber: 107,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\ResultCard.tsx\",\n                                        lineNumber: 106,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\ResultCard.tsx\",\n                                lineNumber: 104,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: totalPossibleScore\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\ResultCard.tsx\",\n                                lineNumber: 110,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\ResultCard.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\ResultCard.tsx\",\n                lineNumber: 80,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-gray-900 mb-3\",\n                        children: \"分类得分详情\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\ResultCard.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: result.scores.map((score, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between p-3 bg-gray-50 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_RotateCcw_Star_Trash2_Trophy_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"w-4 h-4 text-yellow-500\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\ResultCard.tsx\",\n                                                lineNumber: 121,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium text-gray-900\",\n                                                children: score.category\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\ResultCard.tsx\",\n                                                lineNumber: 122,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\ResultCard.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-right\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"font-medium text-gray-900\",\n                                                children: [\n                                                    score.score,\n                                                    \" / \",\n                                                    score.total\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\ResultCard.tsx\",\n                                                lineNumber: 125,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-gray-500\",\n                                                children: [\n                                                    (score.score / score.total * 100).toFixed(1),\n                                                    \"%\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\ResultCard.tsx\",\n                                                lineNumber: 128,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\ResultCard.tsx\",\n                                        lineNumber: 124,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, index, true, {\n                                fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\ResultCard.tsx\",\n                                lineNumber: 119,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\ResultCard.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\ResultCard.tsx\",\n                lineNumber: 115,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onRestart,\n                        className: \"w-full btn-primary py-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_RotateCcw_Star_Trash2_Trophy_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"w-4 h-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\ResultCard.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 11\n                            }, this),\n                            \"重新答题\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\ResultCard.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 9\n                    }, this),\n                    isPassed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setShowClearDialog(true),\n                        className: \"w-full btn-outline py-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_RotateCcw_Star_Trash2_Trophy_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"w-4 h-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\ResultCard.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 13\n                            }, this),\n                            \"清除登录信息和API密钥\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\ResultCard.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\ResultCard.tsx\",\n                lineNumber: 138,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm text-blue-800\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"font-medium mb-2\",\n                            children: isPassed ? \"\\uD83C\\uDF8A 成功提示：\" : \"\\uD83D\\uDCA1 重试建议：\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\ResultCard.tsx\",\n                            lineNumber: 161,\n                            columnNumber: 11\n                        }, this),\n                        isPassed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            className: \"space-y-1 text-xs\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"• 您已成功获得哔哩哔哩硬核会员资格\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\ResultCard.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"• 为了保护您的隐私，建议清除本地保存的登录信息\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\ResultCard.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"• 感谢使用本工具，祝您使用愉快！\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\ResultCard.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\ResultCard.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            className: \"space-y-1 text-xs\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"• 建议选择知识区和历史区，这些分类的正确率更高\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\ResultCard.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"• 每日最多可以答题3次，请合理安排\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\ResultCard.tsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"• 可以尝试更换不同的AI模型来提高准确率\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\ResultCard.tsx\",\n                                    lineNumber: 174,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\ResultCard.tsx\",\n                            lineNumber: 171,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\ResultCard.tsx\",\n                    lineNumber: 160,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\ResultCard.tsx\",\n                lineNumber: 159,\n                columnNumber: 7\n            }, this),\n            showClearDialog && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg p-6 max-w-md mx-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_RotateCcw_Star_Trash2_Trophy_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"w-6 h-6 text-yellow-600\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\ResultCard.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\ResultCard.tsx\",\n                                    lineNumber: 185,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium text-gray-900 mb-2\",\n                                    children: \"确认清除数据\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\ResultCard.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: \"此操作将清除所有本地保存的登录信息和API密钥，确保您的隐私安全。此操作不可撤销。\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\ResultCard.tsx\",\n                                    lineNumber: 191,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\ResultCard.tsx\",\n                            lineNumber: 184,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowClearDialog(false),\n                                    disabled: isClearing,\n                                    className: \"flex-1 btn-outline py-2 disabled:opacity-50\",\n                                    children: \"取消\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\ResultCard.tsx\",\n                                    lineNumber: 197,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleClearData,\n                                    disabled: isClearing,\n                                    className: \"flex-1 btn-primary py-2 disabled:opacity-50\",\n                                    children: isClearing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-4 h-4 loading-spinner border-white\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\ResultCard.tsx\",\n                                                lineNumber: 211,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"清除中...\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\ResultCard.tsx\",\n                                                lineNumber: 212,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\ResultCard.tsx\",\n                                        lineNumber: 210,\n                                        columnNumber: 19\n                                    }, this) : \"确认清除\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\ResultCard.tsx\",\n                                    lineNumber: 204,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\ResultCard.tsx\",\n                            lineNumber: 196,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\ResultCard.tsx\",\n                    lineNumber: 183,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\ResultCard.tsx\",\n                lineNumber: 182,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\components\\\\ResultCard.tsx\",\n        lineNumber: 52,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ResultCard.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/ai.ts":
/*!***********************!*\
  !*** ./src/lib/ai.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DeepSeekAPI: () => (/* binding */ DeepSeekAPI),\n/* harmony export */   GeminiAPI: () => (/* binding */ GeminiAPI),\n/* harmony export */   OpenAIAPI: () => (/* binding */ OpenAIAPI),\n/* harmony export */   clearAIConfig: () => (/* binding */ clearAIConfig),\n/* harmony export */   createAIInstance: () => (/* binding */ createAIInstance),\n/* harmony export */   loadAIConfig: () => (/* binding */ loadAIConfig),\n/* harmony export */   saveAIConfig: () => (/* binding */ saveAIConfig),\n/* harmony export */   validateAIConfig: () => (/* binding */ validateAIConfig)\n/* harmony export */ });\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/types */ \"(ssr)/./src/types/index.ts\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n// AI提示词模板\nconst PROMPT_TEMPLATE = `\n当前时间：{timestamp}\n你是一个高效精准的答题专家，面对选择题时，直接根据问题和选项判断正确答案，并返回对应选项的序号（1, 2, 3, 4）。示例：\n问题：大的反义词是什么？\n选项：['长', '宽', '小', '热']\n回答：3\n如果不确定正确答案，选择最接近的选项序号返回，不提供额外解释或超出 1-4 的内容。\n---\n请回答我的问题：{question}\n`;\n// DeepSeek API\nclass DeepSeekAPI {\n    constructor(apiKey){\n        this.baseUrl = \"https://api.deepseek.com/v1\";\n        this.model = \"deepseek-chat\";\n        this.apiKey = apiKey;\n    }\n    async ask(question, timeout = 30000) {\n        const url = `${this.baseUrl}/chat/completions`;\n        const headers = {\n            \"Content-Type\": \"application/json\",\n            \"Authorization\": `Bearer ${this.apiKey}`\n        };\n        const data = {\n            model: this.model,\n            messages: [\n                {\n                    role: \"user\",\n                    content: PROMPT_TEMPLATE.replace(\"{timestamp}\", Date.now().toString()).replace(\"{question}\", question)\n                }\n            ]\n        };\n        try {\n            const controller = new AbortController();\n            const timeoutId = setTimeout(()=>controller.abort(), timeout);\n            const response = await fetch(url, {\n                method: \"POST\",\n                headers,\n                body: JSON.stringify(data),\n                signal: controller.signal\n            });\n            clearTimeout(timeoutId);\n            if (!response.ok) {\n                throw new Error(`DeepSeek API error: ${response.status} ${response.statusText}`);\n            }\n            const result = await response.json();\n            return result.choices[0].message.content.trim();\n        } catch (error) {\n            console.error(\"DeepSeek API request failed:\", error);\n            throw new Error(`DeepSeek API请求失败: ${error instanceof Error ? error.message : \"未知错误\"}`);\n        }\n    }\n}\n// Gemini API\nclass GeminiAPI {\n    constructor(apiKey){\n        this.baseUrl = \"https://generativelanguage.googleapis.com/v1beta\";\n        this.model = \"gemini-2.0-flash-exp\";\n        this.apiKey = apiKey;\n    }\n    async ask(question, timeout = 30000) {\n        const url = `${this.baseUrl}/models/${this.model}:generateContent?key=${this.apiKey}`;\n        const headers = {\n            \"Content-Type\": \"application/json\"\n        };\n        const data = {\n            contents: [\n                {\n                    parts: [\n                        {\n                            text: PROMPT_TEMPLATE.replace(\"{timestamp}\", Date.now().toString()).replace(\"{question}\", question)\n                        }\n                    ]\n                }\n            ]\n        };\n        try {\n            const controller = new AbortController();\n            const timeoutId = setTimeout(()=>controller.abort(), timeout);\n            const response = await fetch(url, {\n                method: \"POST\",\n                headers,\n                body: JSON.stringify(data),\n                signal: controller.signal\n            });\n            clearTimeout(timeoutId);\n            if (!response.ok) {\n                throw new Error(`Gemini API error: ${response.status} ${response.statusText}`);\n            }\n            const result = await response.json();\n            return result.candidates[0].content.parts[0].text.trim();\n        } catch (error) {\n            console.error(\"Gemini API request failed:\", error);\n            throw new Error(`Gemini API请求失败: ${error instanceof Error ? error.message : \"未知错误\"}`);\n        }\n    }\n}\n// OpenAI API (兼容格式)\nclass OpenAIAPI {\n    constructor(baseUrl, model, apiKey){\n        this.baseUrl = baseUrl.endsWith(\"/\") ? baseUrl.slice(0, -1) : baseUrl;\n        this.model = model;\n        this.apiKey = apiKey;\n    }\n    async ask(question, timeout = 30000) {\n        const url = `${this.baseUrl}/chat/completions`;\n        const headers = {\n            \"Content-Type\": \"application/json\",\n            \"Authorization\": `Bearer ${this.apiKey}`\n        };\n        const data = {\n            model: this.model,\n            messages: [\n                {\n                    role: \"user\",\n                    content: PROMPT_TEMPLATE.replace(\"{timestamp}\", Date.now().toString()).replace(\"{question}\", question)\n                }\n            ]\n        };\n        try {\n            const controller = new AbortController();\n            const timeoutId = setTimeout(()=>controller.abort(), timeout);\n            const response = await fetch(url, {\n                method: \"POST\",\n                headers,\n                body: JSON.stringify(data),\n                signal: controller.signal\n            });\n            clearTimeout(timeoutId);\n            if (!response.ok) {\n                throw new Error(`OpenAI API error: ${response.status} ${response.statusText}`);\n            }\n            const result = await response.json();\n            return result.choices[0].message.content.trim();\n        } catch (error) {\n            console.error(\"OpenAI API request failed:\", error);\n            throw new Error(`OpenAI API请求失败: ${error instanceof Error ? error.message : \"未知错误\"}`);\n        }\n    }\n}\n// AI配置管理\nfunction saveAIConfig(config) {\n    try {\n        _utils__WEBPACK_IMPORTED_MODULE_1__.storage.set(_types__WEBPACK_IMPORTED_MODULE_0__.STORAGE_KEYS.AI_CONFIG, config);\n    } catch (error) {\n        console.error(\"保存AI配置失败:\", error);\n        throw error;\n    }\n}\nfunction loadAIConfig() {\n    try {\n        return _utils__WEBPACK_IMPORTED_MODULE_1__.storage.get(_types__WEBPACK_IMPORTED_MODULE_0__.STORAGE_KEYS.AI_CONFIG);\n    } catch (error) {\n        console.error(\"加载AI配置失败:\", error);\n        return null;\n    }\n}\nfunction clearAIConfig() {\n    try {\n        _utils__WEBPACK_IMPORTED_MODULE_1__.storage.remove(_types__WEBPACK_IMPORTED_MODULE_0__.STORAGE_KEYS.AI_CONFIG);\n    } catch (error) {\n        console.error(\"清除AI配置失败:\", error);\n    }\n}\n// 创建AI实例\nfunction createAIInstance(config) {\n    switch(config.model_choice){\n        case \"1\":\n            if (!config.api_key_deepseek) {\n                throw new Error(\"DeepSeek API密钥未配置\");\n            }\n            return new DeepSeekAPI(config.api_key_deepseek);\n        case \"2\":\n            if (!config.api_key_gemini) {\n                throw new Error(\"Gemini API密钥未配置\");\n            }\n            return new GeminiAPI(config.api_key_gemini);\n        case \"3\":\n            if (!config.openai_config) {\n                throw new Error(\"OpenAI配置未完整\");\n            }\n            return new OpenAIAPI(config.openai_config.base_url, config.openai_config.model, config.openai_config.api_key);\n        default:\n            throw new Error(\"无效的AI模型选择\");\n    }\n}\n// 验证AI配置\nfunction validateAIConfig(config) {\n    switch(config.model_choice){\n        case \"1\":\n            if (!config.api_key_deepseek) {\n                return {\n                    isValid: false,\n                    error: \"DeepSeek API密钥不能为空\"\n                };\n            }\n            if (!(0,_utils__WEBPACK_IMPORTED_MODULE_1__.validateApiKey)(config.api_key_deepseek, \"deepseek\")) {\n                return {\n                    isValid: false,\n                    error: \"DeepSeek API密钥格式不正确\"\n                };\n            }\n            break;\n        case \"2\":\n            if (!config.api_key_gemini) {\n                return {\n                    isValid: false,\n                    error: \"Gemini API密钥不能为空\"\n                };\n            }\n            if (!(0,_utils__WEBPACK_IMPORTED_MODULE_1__.validateApiKey)(config.api_key_gemini, \"gemini\")) {\n                return {\n                    isValid: false,\n                    error: \"Gemini API密钥格式不正确\"\n                };\n            }\n            break;\n        case \"3\":\n            if (!config.openai_config) {\n                return {\n                    isValid: false,\n                    error: \"OpenAI配置不能为空\"\n                };\n            }\n            const { base_url, model, api_key } = config.openai_config;\n            if (!base_url || !model || !api_key) {\n                return {\n                    isValid: false,\n                    error: \"OpenAI配置不完整\"\n                };\n            }\n            if (!(0,_utils__WEBPACK_IMPORTED_MODULE_1__.validateApiKey)(api_key, \"openai\")) {\n                return {\n                    isValid: false,\n                    error: \"OpenAI API密钥格式不正确\"\n                };\n            }\n            break;\n        default:\n            return {\n                isValid: false,\n                error: \"无效的AI模型选择\"\n            };\n    }\n    return {\n        isValid: true\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/ai.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/api.ts":
/*!************************!*\
  !*** ./src/lib/api.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   apiGet: () => (/* binding */ apiGet),\n/* harmony export */   apiPost: () => (/* binding */ apiPost),\n/* harmony export */   setAuthData: () => (/* binding */ setAuthData)\n/* harmony export */ });\n// API配置\nconst API_CONFIG = {\n    appkey: \"783bbb7264451d82\",\n    appsec: \"2653583c8873dea268ab9386918b1d65\",\n    user_agent: \"Mozilla/5.0 BiliDroid/1.12.0 (<EMAIL>)\"\n};\n// 请求头配置\nconst DEFAULT_HEADERS = {\n    \"User-Agent\": API_CONFIG.user_agent,\n    \"Content-Type\": \"application/x-www-form-urlencoded\",\n    \"Accept\": \"application/json\",\n    \"Accept-Language\": \"zh-CN,zh;q=0.9,en;q=0.8\",\n    \"x-bili-metadata-legal-region\": \"CN\",\n    \"x-bili-aurora-eid\": \"\",\n    \"x-bili-aurora-zone\": \"\"\n};\n// 全局认证信息\nlet globalAuthData = null;\nfunction setAuthData(authData) {\n    globalAuthData = authData;\n}\n// 生成bili_ticket\nfunction generateBiliTicket() {\n    const timestamp = Math.floor(Date.now() / 1000);\n    const random = Math.random().toString(36).substring(2, 15);\n    return `${timestamp}_${random}`;\n}\n// GET请求\nasync function apiGet(url, params = {}) {\n    try {\n        const searchParams = new URLSearchParams();\n        Object.entries(params).forEach(([key, value])=>{\n            if (value !== undefined && value !== null) {\n                searchParams.append(key, String(value));\n            }\n        });\n        const headers = {\n            ...DEFAULT_HEADERS\n        };\n        if (globalAuthData) {\n            headers[\"x-bili-mid\"] = globalAuthData.mid;\n            headers[\"cookie\"] = globalAuthData.cookie;\n        }\n        headers[\"x-bili-ticket\"] = generateBiliTicket();\n        const fullUrl = `${url}?${searchParams.toString()}`;\n        const response = await fetch(fullUrl, {\n            method: \"GET\",\n            headers,\n            mode: \"cors\"\n        });\n        if (!response.ok) {\n            throw new Error(`HTTP error! status: ${response.status}`);\n        }\n        const data = await response.json();\n        return data;\n    } catch (error) {\n        console.error(\"API GET request failed:\", error);\n        throw error;\n    }\n}\n// POST请求\nasync function apiPost(url, data = {}) {\n    try {\n        const formData = new URLSearchParams();\n        Object.entries(data).forEach(([key, value])=>{\n            if (value !== undefined && value !== null) {\n                formData.append(key, String(value));\n            }\n        });\n        const headers = {\n            ...DEFAULT_HEADERS\n        };\n        if (globalAuthData) {\n            headers[\"x-bili-mid\"] = globalAuthData.mid;\n            headers[\"cookie\"] = globalAuthData.cookie;\n        }\n        headers[\"x-bili-ticket\"] = generateBiliTicket();\n        const response = await fetch(url, {\n            method: \"POST\",\n            headers,\n            body: formData,\n            mode: \"cors\"\n        });\n        if (!response.ok) {\n            throw new Error(`HTTP error! status: ${response.status}`);\n        }\n        const responseData = await response.json();\n        return responseData;\n    } catch (error) {\n        console.error(\"API POST request failed:\", error);\n        throw error;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/api.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clearAuthData: () => (/* binding */ clearAuthData),\n/* harmony export */   getQRCode: () => (/* binding */ getQRCode),\n/* harmony export */   handleQRCodeLogin: () => (/* binding */ handleQRCodeLogin),\n/* harmony export */   loadAuthData: () => (/* binding */ loadAuthData),\n/* harmony export */   pollQRCode: () => (/* binding */ pollQRCode),\n/* harmony export */   saveAuthData: () => (/* binding */ saveAuthData),\n/* harmony export */   validateAuth: () => (/* binding */ validateAuth)\n/* harmony export */ });\n/* harmony import */ var _api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./api */ \"(ssr)/./src/lib/api.ts\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils */ \"(ssr)/./src/lib/utils.ts\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/types */ \"(ssr)/./src/types/index.ts\");\n\n\n\n// 获取二维码登录信息\nasync function getQRCode() {\n    try {\n        // 使用正确的B站二维码API\n        const response = await fetch(\"https://passport.bilibili.com/x/passport-login/web/qrcode/generate\", {\n            method: \"GET\",\n            headers: {\n                \"User-Agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36\",\n                \"Referer\": \"https://www.bilibili.com/\"\n            }\n        });\n        if (!response.ok) {\n            throw new Error(`HTTP error! status: ${response.status}`);\n        }\n        const data = await response.json();\n        if (data.code !== 0) {\n            throw new Error(data.message || \"获取二维码失败\");\n        }\n        return {\n            url: data.data.url,\n            auth_code: data.data.qrcode_key\n        };\n    } catch (error) {\n        console.error(\"获取二维码失败:\", error);\n        throw error;\n    }\n}\n// 轮询二维码登录状态\nasync function pollQRCode(authCode) {\n    try {\n        const response = await fetch(`https://passport.bilibili.com/x/passport-login/web/qrcode/poll?qrcode_key=${authCode}`, {\n            method: \"GET\",\n            headers: {\n                \"User-Agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36\",\n                \"Referer\": \"https://www.bilibili.com/\"\n            }\n        });\n        if (!response.ok) {\n            throw new Error(`HTTP error! status: ${response.status}`);\n        }\n        const data = await response.json();\n        return data;\n    } catch (error) {\n        console.error(\"轮询二维码状态失败:\", error);\n        throw error;\n    }\n}\n// 保存认证信息到本地存储\nfunction saveAuthData(authData) {\n    try {\n        const dataWithTimestamp = {\n            ...authData,\n            timestamp: Date.now()\n        };\n        _utils__WEBPACK_IMPORTED_MODULE_1__.storage.set(_types__WEBPACK_IMPORTED_MODULE_2__.STORAGE_KEYS.AUTH_DATA, dataWithTimestamp);\n        (0,_api__WEBPACK_IMPORTED_MODULE_0__.setAuthData)(authData);\n    } catch (error) {\n        console.error(\"保存认证信息失败:\", error);\n        throw error;\n    }\n}\n// 从本地存储加载认证信息\nfunction loadAuthData() {\n    try {\n        const data = _utils__WEBPACK_IMPORTED_MODULE_1__.storage.get(_types__WEBPACK_IMPORTED_MODULE_2__.STORAGE_KEYS.AUTH_DATA);\n        if (!data) {\n            return null;\n        }\n        // 检查是否过期（7天）\n        if ((0,_utils__WEBPACK_IMPORTED_MODULE_1__.isAuthExpired)(data.timestamp)) {\n            _utils__WEBPACK_IMPORTED_MODULE_1__.storage.remove(_types__WEBPACK_IMPORTED_MODULE_2__.STORAGE_KEYS.AUTH_DATA);\n            return null;\n        }\n        const authData = {\n            access_token: data.access_token,\n            csrf: data.csrf,\n            mid: data.mid,\n            cookie: data.cookie\n        };\n        (0,_api__WEBPACK_IMPORTED_MODULE_0__.setAuthData)(authData);\n        return authData;\n    } catch (error) {\n        console.error(\"加载认证信息失败:\", error);\n        return null;\n    }\n}\n// 清除认证信息\nfunction clearAuthData() {\n    try {\n        _utils__WEBPACK_IMPORTED_MODULE_1__.storage.remove(_types__WEBPACK_IMPORTED_MODULE_2__.STORAGE_KEYS.AUTH_DATA);\n        (0,_api__WEBPACK_IMPORTED_MODULE_0__.setAuthData)(null);\n    } catch (error) {\n        console.error(\"清除认证信息失败:\", error);\n    }\n}\n// 验证认证信息是否有效\nasync function validateAuth() {\n    try {\n        const authData = loadAuthData();\n        if (!authData) {\n            return false;\n        }\n        // 这里可以添加一个简单的API调用来验证token是否有效\n        // 暂时返回true，实际使用时可以调用用户信息接口验证\n        return true;\n    } catch (error) {\n        console.error(\"验证认证信息失败:\", error);\n        return false;\n    }\n}\n// 处理二维码登录流程\nasync function handleQRCodeLogin() {\n    return new Promise(async (resolve, reject)=>{\n        try {\n            const qrData = await getQRCode();\n            let retryCount = 0;\n            const maxRetries = 60; // 最大重试次数\n            const pollInterval = setInterval(async ()=>{\n                try {\n                    if (retryCount >= maxRetries) {\n                        clearInterval(pollInterval);\n                        reject(new Error(\"二维码登录超时\"));\n                        return;\n                    }\n                    const pollResult = await pollQRCode(qrData.auth_code);\n                    if (pollResult.code === 0 && pollResult.data) {\n                        clearInterval(pollInterval);\n                        // 构建认证数据\n                        const cookies = pollResult.data.cookie_info.cookies;\n                        let csrf = \"\";\n                        for (const cookie of cookies){\n                            if (cookie.name === \"bili_jct\") {\n                                csrf = cookie.value;\n                                break;\n                            }\n                        }\n                        const cookieString = cookies.map((cookie)=>`${cookie.name}=${cookie.value}`).join(\";\");\n                        const authData = {\n                            access_token: pollResult.data.access_token,\n                            csrf,\n                            mid: String(pollResult.data.mid),\n                            cookie: cookieString\n                        };\n                        saveAuthData(authData);\n                        resolve(authData);\n                    } else if (pollResult.code === 86101) {\n                        // 二维码未扫描，继续轮询\n                        retryCount++;\n                    } else if (pollResult.code === 86090) {\n                        // 二维码已扫描但未确认，继续轮询\n                        retryCount++;\n                    } else if (pollResult.code === 86038) {\n                        // 二维码已过期\n                        clearInterval(pollInterval);\n                        reject(new Error(\"二维码已过期，请重新获取\"));\n                    } else {\n                        // 其他错误\n                        clearInterval(pollInterval);\n                        reject(new Error(pollResult.message || \"登录失败\"));\n                    }\n                } catch (error) {\n                    retryCount++;\n                    console.error(\"轮询二维码状态失败:\", error);\n                }\n            }, 1000); // 每秒轮询一次\n            // 返回二维码数据供组件使用\n            resolve.qrData = qrData;\n        } catch (error) {\n            reject(error);\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/qrcode.ts":
/*!***************************!*\
  !*** ./src/lib/qrcode.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generateQRCodeASCII: () => (/* binding */ generateQRCodeASCII),\n/* harmony export */   generateQRCodeDataURL: () => (/* binding */ generateQRCodeDataURL),\n/* harmony export */   generateQRCodeSVG: () => (/* binding */ generateQRCodeSVG)\n/* harmony export */ });\n/* harmony import */ var qrcode__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! qrcode */ \"(ssr)/./node_modules/qrcode/lib/index.js\");\n\n// 生成二维码数据URL\nasync function generateQRCodeDataURL(text) {\n    try {\n        const options = {\n            errorCorrectionLevel: \"M\",\n            type: \"image/png\",\n            quality: 0.92,\n            margin: 1,\n            color: {\n                dark: \"#000000\",\n                light: \"#FFFFFF\"\n            },\n            width: 256\n        };\n        const dataURL = await qrcode__WEBPACK_IMPORTED_MODULE_0__.toDataURL(text, options);\n        return dataURL;\n    } catch (error) {\n        console.error(\"生成二维码失败:\", error);\n        throw new Error(\"生成二维码失败\");\n    }\n}\n// 生成二维码SVG\nasync function generateQRCodeSVG(text) {\n    try {\n        const options = {\n            errorCorrectionLevel: \"M\",\n            type: \"svg\",\n            margin: 1,\n            color: {\n                dark: \"#000000\",\n                light: \"#FFFFFF\"\n            },\n            width: 256\n        };\n        const svg = await qrcode__WEBPACK_IMPORTED_MODULE_0__.toString(text, options);\n        return svg;\n    } catch (error) {\n        console.error(\"生成二维码SVG失败:\", error);\n        throw new Error(\"生成二维码SVG失败\");\n    }\n}\n// 生成ASCII二维码（用于控制台显示）\nasync function generateQRCodeASCII(text) {\n    try {\n        const options = {\n            type: \"terminal\",\n            small: true\n        };\n        const ascii = await qrcode__WEBPACK_IMPORTED_MODULE_0__.toString(text, options);\n        return ascii;\n    } catch (error) {\n        console.error(\"生成ASCII二维码失败:\", error);\n        throw new Error(\"生成ASCII二维码失败\");\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/qrcode.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/quiz.ts":
/*!*************************!*\
  !*** ./src/lib/quiz.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clearQuizSession: () => (/* binding */ clearQuizSession),\n/* harmony export */   formatQuestionForAI: () => (/* binding */ formatQuestionForAI),\n/* harmony export */   getCaptcha: () => (/* binding */ getCaptcha),\n/* harmony export */   getCategories: () => (/* binding */ getCategories),\n/* harmony export */   getQuestion: () => (/* binding */ getQuestion),\n/* harmony export */   getQuizResult: () => (/* binding */ getQuizResult),\n/* harmony export */   loadQuizSession: () => (/* binding */ loadQuizSession),\n/* harmony export */   parseAIAnswer: () => (/* binding */ parseAIAnswer),\n/* harmony export */   saveQuizSession: () => (/* binding */ saveQuizSession),\n/* harmony export */   submitAnswer: () => (/* binding */ submitAnswer),\n/* harmony export */   submitCaptcha: () => (/* binding */ submitCaptcha)\n/* harmony export */ });\n/* harmony import */ var _api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./api */ \"(ssr)/./src/lib/api.ts\");\n\n// 获取分类信息\nasync function getCategories() {\n    try {\n        const response = await (0,_api__WEBPACK_IMPORTED_MODULE_0__.apiGet)(\"https://api.bilibili.com/x/senior/v1/category\", {\n            disable_rcmd: 0,\n            mobi_app: \"android\",\n            platform: \"android\",\n            statistics: '{\"appId\":1,\"platform\":3,\"version\":\"8.40.0\",\"abtest\":\"\"}',\n            web_location: \"333.790\"\n        });\n        if (response.code === 0 && response.data) {\n            return response.data.categories || [];\n        } else if (response.code === 41099) {\n            throw new Error(\"获取分类失败，可能是已经达到答题限制(B站每日限制3次)，请前往B站APP确认是否可以正常答题\");\n        } else {\n            throw new Error(response.message || \"获取分类失败，请前往B站APP确认是否可以正常答题\");\n        }\n    } catch (error) {\n        console.error(\"获取分类失败:\", error);\n        throw error;\n    }\n}\n// 获取验证码\nasync function getCaptcha() {\n    try {\n        const response = await (0,_api__WEBPACK_IMPORTED_MODULE_0__.apiGet)(\"https://api.bilibili.com/x/senior/v1/captcha\", {\n            disable_rcmd: 0,\n            mobi_app: \"android\",\n            platform: \"android\",\n            statistics: '{\"appId\":1,\"platform\":3,\"version\":\"8.40.0\",\"abtest\":\"\"}',\n            web_location: \"333.790\"\n        });\n        if (response.code === 0 && response.data) {\n            return response.data;\n        } else {\n            throw new Error(response.message || \"获取验证码失败，请前往B站APP确认是否可以正常答题\");\n        }\n    } catch (error) {\n        console.error(\"获取验证码失败:\", error);\n        throw error;\n    }\n}\n// 提交验证码\nasync function submitCaptcha(code, captchaToken, categoryIds) {\n    try {\n        const response = await (0,_api__WEBPACK_IMPORTED_MODULE_0__.apiPost)(\"https://api.bilibili.com/x/senior/v1/captcha/submit\", {\n            bili_code: code,\n            bili_token: captchaToken,\n            disable_rcmd: \"0\",\n            gt_challenge: \"\",\n            gt_seccode: \"\",\n            gt_validate: \"\",\n            ids: categoryIds,\n            mobi_app: \"android\",\n            platform: \"android\",\n            statistics: '{\"appId\":1,\"platform\":3,\"version\":\"8.40.0\",\"abtest\":\"\"}',\n            type: \"bilibili\"\n        });\n        return response.code === 0;\n    } catch (error) {\n        console.error(\"提交验证码失败:\", error);\n        throw error;\n    }\n}\n// 获取题目\nasync function getQuestion() {\n    try {\n        const response = await (0,_api__WEBPACK_IMPORTED_MODULE_0__.apiGet)(\"https://api.bilibili.com/x/senior/v1/question\", {\n            disable_rcmd: \"0\",\n            mobi_app: \"android\",\n            platform: \"android\",\n            statistics: '{\"appId\":1,\"platform\":3,\"version\":\"8.40.0\",\"abtest\":\"\"}',\n            web_location: \"333.790\"\n        });\n        return response;\n    } catch (error) {\n        console.error(\"获取题目失败:\", error);\n        throw error;\n    }\n}\n// 提交答案\nasync function submitAnswer(questionId, ansHash, ansText) {\n    try {\n        const response = await (0,_api__WEBPACK_IMPORTED_MODULE_0__.apiPost)(\"https://api.bilibili.com/x/senior/v1/answer/submit\", {\n            id: questionId,\n            ans_hash: ansHash,\n            ans_text: ansText,\n            disable_rcmd: \"0\",\n            mobi_app: \"android\",\n            platform: \"android\",\n            statistics: '{\"appId\":1,\"platform\":3,\"version\":\"8.40.0\",\"abtest\":\"\"}',\n            web_location: \"333.790\"\n        });\n        return response;\n    } catch (error) {\n        console.error(\"提交答案失败:\", error);\n        throw error;\n    }\n}\n// 获取答题结果\nasync function getQuizResult() {\n    try {\n        const response = await (0,_api__WEBPACK_IMPORTED_MODULE_0__.apiGet)(\"https://api.bilibili.com/x/senior/v1/answer/result\", {\n            disable_rcmd: \"0\",\n            mobi_app: \"android\",\n            platform: \"android\",\n            statistics: '{\"appId\":1,\"platform\":3,\"version\":\"8.40.0\",\"abtest\":\"\"}',\n            web_location: \"333.790\"\n        });\n        if (response.code === 0 && response.data) {\n            return response.data;\n        } else {\n            throw new Error(response.message || \"获取答题结果失败\");\n        }\n    } catch (error) {\n        console.error(\"获取答题结果失败:\", error);\n        throw error;\n    }\n}\n// 格式化题目用于AI提问\nfunction formatQuestionForAI(question) {\n    const optionsText = question.answers.map((answer, index)=>`${index + 1}. ${answer.ans_text}`).join(\"\\n\");\n    return `题目: ${question.question}\\n选项:\\n${optionsText}`;\n}\n// 解析AI回答\nfunction parseAIAnswer(aiResponse, answersCount) {\n    // 清理回答文本\n    const cleanResponse = aiResponse.trim();\n    // 尝试提取数字\n    const numberMatch = cleanResponse.match(/\\b([1-4])\\b/);\n    if (numberMatch) {\n        const num = parseInt(numberMatch[1]);\n        if (num >= 1 && num <= answersCount) {\n            return num;\n        }\n    }\n    // 尝试从开头提取数字\n    const firstChar = cleanResponse.charAt(0);\n    if (/[1-4]/.test(firstChar)) {\n        const num = parseInt(firstChar);\n        if (num >= 1 && num <= answersCount) {\n            return num;\n        }\n    }\n    return null;\n}\nconst QUIZ_SESSION_KEY = \"bili-hardcore-quiz-session\";\nfunction saveQuizSession(session) {\n    try {\n        localStorage.setItem(QUIZ_SESSION_KEY, JSON.stringify(session));\n    } catch (error) {\n        console.error(\"保存答题会话失败:\", error);\n    }\n}\nfunction loadQuizSession() {\n    try {\n        const data = localStorage.getItem(QUIZ_SESSION_KEY);\n        return data ? JSON.parse(data) : null;\n    } catch (error) {\n        console.error(\"加载答题会话失败:\", error);\n        return null;\n    }\n}\nfunction clearQuizSession() {\n    try {\n        localStorage.removeItem(QUIZ_SESSION_KEY);\n    } catch (error) {\n        console.error(\"清除答题会话失败:\", error);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/quiz.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   delay: () => (/* binding */ delay),\n/* harmony export */   formatTime: () => (/* binding */ formatTime),\n/* harmony export */   generateRandomString: () => (/* binding */ generateRandomString),\n/* harmony export */   handleError: () => (/* binding */ handleError),\n/* harmony export */   isAuthExpired: () => (/* binding */ isAuthExpired),\n/* harmony export */   storage: () => (/* binding */ storage),\n/* harmony export */   validateApiKey: () => (/* binding */ validateApiKey)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n// 本地存储工具函数\nconst storage = {\n    get: (key)=>{\n        if (true) return null;\n        try {\n            const item = localStorage.getItem(key);\n            return item ? JSON.parse(item) : null;\n        } catch (error) {\n            console.error(`Error getting item from localStorage:`, error);\n            return null;\n        }\n    },\n    set: (key, value)=>{\n        if (true) return;\n        try {\n            localStorage.setItem(key, JSON.stringify(value));\n        } catch (error) {\n            console.error(`Error setting item to localStorage:`, error);\n        }\n    },\n    remove: (key)=>{\n        if (true) return;\n        try {\n            localStorage.removeItem(key);\n        } catch (error) {\n            console.error(`Error removing item from localStorage:`, error);\n        }\n    },\n    clear: ()=>{\n        if (true) return;\n        try {\n            localStorage.clear();\n        } catch (error) {\n            console.error(`Error clearing localStorage:`, error);\n        }\n    }\n};\n// 时间格式化\nfunction formatTime(timestamp) {\n    return new Date(timestamp).toLocaleString(\"zh-CN\");\n}\n// 延迟函数\nfunction delay(ms) {\n    return new Promise((resolve)=>setTimeout(resolve, ms));\n}\n// 生成随机字符串\nfunction generateRandomString(length = 8) {\n    const chars = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789\";\n    let result = \"\";\n    for(let i = 0; i < length; i++){\n        result += chars.charAt(Math.floor(Math.random() * chars.length));\n    }\n    return result;\n}\n// 错误处理\nfunction handleError(error) {\n    if (error instanceof Error) {\n        return error.message;\n    }\n    if (typeof error === \"string\") {\n        return error;\n    }\n    return \"发生未知错误\";\n}\n// 验证API密钥格式\nfunction validateApiKey(key, type) {\n    if (!key || typeof key !== \"string\") return false;\n    switch(type){\n        case \"deepseek\":\n            return key.startsWith(\"sk-\") && key.length > 10;\n        case \"gemini\":\n            return key.length > 20;\n        case \"openai\":\n            return key.startsWith(\"sk-\") && key.length > 20;\n        default:\n            return false;\n    }\n}\n// 检查认证信息是否过期（7天）\nfunction isAuthExpired(timestamp) {\n    const now = Date.now();\n    const sevenDays = 7 * 24 * 60 * 60 * 1000;\n    return now - timestamp > sevenDays;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(ssr)/./src/types/index.ts":
/*!****************************!*\
  !*** ./src/types/index.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   STORAGE_KEYS: () => (/* binding */ STORAGE_KEYS)\n/* harmony export */ });\n// 用户认证相关类型\n// 本地存储键名\nconst STORAGE_KEYS = {\n    AUTH_DATA: \"bili-hardcore-auth\",\n    AI_CONFIG: \"bili-hardcore-ai-config\",\n    QUIZ_PROGRESS: \"bili-hardcore-quiz-progress\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/types/index.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"20bf1af92877\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYmlsaS1oYXJkY29yZS13ZWIvLi9zcmMvYXBwL2dsb2JhbHMuY3NzP2Q0N2IiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIyMGJmMWFmOTI4NzdcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\nconst metadata = {\n    title: \"哔哩哔哩硬核会员答题助手\",\n    description: \"基于AI的哔哩哔哩硬核会员自动答题工具，支持DeepSeek、Gemini、OpenAI等多种AI模型\",\n    keywords: [\n        \"哔哩哔哩\",\n        \"硬核会员\",\n        \"答题\",\n        \"AI\",\n        \"DeepSeek\",\n        \"Gemini\",\n        \"OpenAI\"\n    ],\n    authors: [\n        {\n            name: \"Bili Hardcore Team\"\n        }\n    ],\n    icons: {\n        icon: \"/favicon.ico\"\n    },\n    openGraph: {\n        title: \"哔哩哔哩硬核会员答题助手\",\n        description: \"基于AI的哔哩哔哩硬核会员自动答题工具\",\n        type: \"website\",\n        locale: \"zh_CN\"\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"zh-CN\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen bg-gradient-to-br from-blue-50 via-white to-pink-50\",\n                children: children\n            }, void 0, false, {\n                fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 31,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 30,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\Temp\\\\bili-hardcore\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 29,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`E:\Temp\bili-hardcore\src\app\page.tsx#default`));


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/qrcode","vendor-chunks/lucide-react","vendor-chunks/pngjs","vendor-chunks/tailwind-merge","vendor-chunks/clsx","vendor-chunks/dijkstrajs"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=E%3A%5CTemp%5Cbili-hardcore%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5CTemp%5Cbili-hardcore&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();