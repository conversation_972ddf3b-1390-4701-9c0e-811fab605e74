[{"E:\\Temp\\bili-hardcore\\src\\app\\layout.tsx": "1", "E:\\Temp\\bili-hardcore\\src\\app\\page.tsx": "2", "E:\\Temp\\bili-hardcore\\src\\components\\AIConfigCard.tsx": "3", "E:\\Temp\\bili-hardcore\\src\\components\\CaptchaCard.tsx": "4", "E:\\Temp\\bili-hardcore\\src\\components\\ErrorBoundary.tsx": "5", "E:\\Temp\\bili-hardcore\\src\\components\\LoadingSpinner.tsx": "6", "E:\\Temp\\bili-hardcore\\src\\components\\LoginCard.tsx": "7", "E:\\Temp\\bili-hardcore\\src\\components\\QuizCard.tsx": "8", "E:\\Temp\\bili-hardcore\\src\\components\\ResultCard.tsx": "9", "E:\\Temp\\bili-hardcore\\src\\lib\\ai.ts": "10", "E:\\Temp\\bili-hardcore\\src\\lib\\api.ts": "11", "E:\\Temp\\bili-hardcore\\src\\lib\\auth.ts": "12", "E:\\Temp\\bili-hardcore\\src\\lib\\qrcode.ts": "13", "E:\\Temp\\bili-hardcore\\src\\lib\\quiz.ts": "14", "E:\\Temp\\bili-hardcore\\src\\lib\\utils.ts": "15", "E:\\Temp\\bili-hardcore\\src\\types\\index.ts": "16"}, {"size": 1056, "mtime": 1753881568696, "results": "17", "hashOfConfig": "18"}, {"size": 8454, "mtime": 1753881082209, "results": "19", "hashOfConfig": "18"}, {"size": 11337, "mtime": 1753880476112, "results": "20", "hashOfConfig": "18"}, {"size": 10247, "mtime": 1753881888494, "results": "21", "hashOfConfig": "18"}, {"size": 3006, "mtime": 1753881018704, "results": "22", "hashOfConfig": "18"}, {"size": 656, "mtime": 1753881028637, "results": "23", "hashOfConfig": "18"}, {"size": 7340, "mtime": 1753881723942, "results": "24", "hashOfConfig": "18"}, {"size": 10456, "mtime": 1753881636640, "results": "25", "hashOfConfig": "18"}, {"size": 7874, "mtime": 1753880702498, "results": "26", "hashOfConfig": "18"}, {"size": 7966, "mtime": 1753880427699, "results": "27", "hashOfConfig": "18"}, {"size": 2968, "mtime": 1753880163638, "results": "28", "hashOfConfig": "18"}, {"size": 5931, "mtime": 1753880992946, "results": "29", "hashOfConfig": "18"}, {"size": 1520, "mtime": 1753880237335, "results": "30", "hashOfConfig": "18"}, {"size": 6011, "mtime": 1753880519950, "results": "31", "hashOfConfig": "18"}, {"size": 2690, "mtime": 1753880143787, "results": "32", "hashOfConfig": "18"}, {"size": 1760, "mtime": 1753881944931, "results": "33", "hashOfConfig": "18"}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "15gkher", {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "E:\\Temp\\bili-hardcore\\src\\app\\layout.tsx", [], [], "E:\\Temp\\bili-hardcore\\src\\app\\page.tsx", [], [], "E:\\Temp\\bili-hardcore\\src\\components\\AIConfigCard.tsx", [], [], "E:\\Temp\\bili-hardcore\\src\\components\\CaptchaCard.tsx", [], [], "E:\\Temp\\bili-hardcore\\src\\components\\ErrorBoundary.tsx", [], [], "E:\\Temp\\bili-hardcore\\src\\components\\LoadingSpinner.tsx", [], [], "E:\\Temp\\bili-hardcore\\src\\components\\LoginCard.tsx", ["82"], ["83"], "E:\\Temp\\bili-hardcore\\src\\components\\QuizCard.tsx", [], ["84"], "E:\\Temp\\bili-hardcore\\src\\components\\ResultCard.tsx", [], [], "E:\\Temp\\bili-hardcore\\src\\lib\\ai.ts", [], [], "E:\\Temp\\bili-hardcore\\src\\lib\\api.ts", [], [], "E:\\Temp\\bili-hardcore\\src\\lib\\auth.ts", [], [], "E:\\Temp\\bili-hardcore\\src\\lib\\qrcode.ts", [], [], "E:\\Temp\\bili-hardcore\\src\\lib\\quiz.ts", [], [], "E:\\Temp\\bili-hardcore\\src\\lib\\utils.ts", [], [], "E:\\Temp\\bili-hardcore\\src\\types\\index.ts", [], [], {"ruleId": "85", "severity": 1, "message": "86", "line": 176, "column": 15, "nodeType": "87", "endLine": 183, "endColumn": 17}, {"ruleId": "88", "severity": 1, "message": "89", "line": 126, "column": 6, "nodeType": "90", "endLine": 126, "endColumn": 8, "suggestions": "91", "suppressions": "92"}, {"ruleId": "88", "severity": 1, "message": "93", "line": 163, "column": 6, "nodeType": "90", "endLine": 163, "endColumn": 8, "suggestions": "94", "suppressions": "95"}, "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'generateQRCode'. Either include it or remove the dependency array.", "ArrayExpression", ["96"], ["97"], "React Hook useEffect has a missing dependency: 'startQuiz'. Either include it or remove the dependency array.", ["98"], ["99"], {"desc": "100", "fix": "101"}, {"kind": "102", "justification": "103"}, {"desc": "104", "fix": "105"}, {"kind": "102", "justification": "103"}, "Update the dependencies array to be: [generateQRCode]", {"range": "106", "text": "107"}, "directive", "", "Update the dependencies array to be: [startQuiz]", {"range": "108", "text": "109"}, [3523, 3525], "[generateQRCode]", [4711, 4713], "[startQuiz]"]