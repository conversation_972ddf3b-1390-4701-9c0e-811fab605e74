{"version": 4, "routes": {"/": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/", "dataRoute": "/index.rsc"}}, "dynamicRoutes": {}, "notFoundRoutes": [], "preview": {"previewModeId": "5681ec91ee769e2800428dd37c1560df", "previewModeSigningKey": "0c873b4634d434857641eb494afed548a27afb9a4f631d5305edb1786d95d832", "previewModeEncryptionKey": "8e3d71b6e8908a755252d6519d0daaef8ee1fb5d9fcace74bcef2088a19cb36c"}}